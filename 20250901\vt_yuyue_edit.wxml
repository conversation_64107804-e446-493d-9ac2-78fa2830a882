<view class="page_container">
  <!-- 页面标题 -->
  <view class="split"></view>
  <view class="page-header">
    <view class="split"></view>
    <view class="status-tags-left"></view>
    <view class="status-tags">
      <text class="tag name-id-tag">{{formData.visitor_name}}</text>
      <view style="display: flex;padding-right:20px">
        <text class="tag"> {{formData.status}}</text>
        <image class="fstate" src='../../icon/yjrspl.png' wx:if="{{formData.fstate==0}}"></image>
        <image class="fstate" src='../../icon/yjsp.png' wx:if="{{formData.fstate>0}}"></image>
      </view>
    </view>
  </view>

  <!-- 表单内容 -->
  <form bindsubmit="submitForm">
    <!-- 基本信息 -->
    <view class="section" style="background-color: #fafafa;">

      <view class="form-item">
        <view class="form-label">访客类型:<text style="color: red;">*</text></view>
        <picker bindchange="bindVisitorTypeChange" value="{{vistor_type_index}}" range="{{visitorTypes}}" range-key="label">
          <view class="form-value">
            {{visitorTypes[vistor_type_index].label || '请选择访客类型'}}
          </view>
        </picker>
      </view>
      <view class="form-item" wx:if="{{is_region=='2'}}">
        <view class="form-label">区域:<text style="color: red;">*</text></view>
        <picker bindchange="bindRegionChange" value="{{region_index}}" range="{{quyuoptions}}">
          <view class="form-value">
            {{quyuoptions[region_index] || '请选择区域'}}
          </view>
        </picker>
      </view>
      <view class="form-item">
        <view class="form-label">访问事由:</view>
        <view class="form-value disable">
          {{formData.reason}}
        </view>
      </view>
      <view class="form-item">
        <view class="form-label">随行人数:</view>
        <view class="form-value disable">
          {{formData.num}}
        </view>
      </view>
      <view class="form-item">
        <view class="form-label">受访人:</view>
        <view class="form-value disable">
          {{formData.empname}}/{{formData.empno}}
        </view>
      </view>
      <view class="form-item">
        <view class="form-label">车牌:</view>
        <view class="form-value  ">
          <input type="text" placeholder="请输入车牌号" bindinput="onInput" data-field="car_number" value="{{formData.car_number}}" />
        </view>
      </view>

      <view class="form-item">
        <view class="form-label">来访时间:</view>
        <view class="form-value">
          <picker mode="multiSelector" value="{{dateTime1}}" bindchange="changeDateTime1" bindcolumnchange="changeDateTime1" range="{{dateTimeArray1}}" data-field="begin_time">
            <view class="tui-picker-detail">
              <text>{{formData.begin_time||'请输入开始时间'}}</text>
            </view>
          </picker>
        </view>
      </view>

      <view class="form-item">
        <view class="form-label">结束时间:</view>
        <view class="form-value">
          <picker mode="multiSelector" value="{{dateTime2}}" bindchange="changeDateTime2" bindcolumnchange="changeDateTime2" range="{{dateTimeArray2}}" data-field="end_time">
            <view class="tui-picker-detail">
              <text>{{formData.end_time||'请输入结束时间'}}</text>
            </view>
          </picker>
        </view>
      </view>
      <view class="form-item">
        <view class="form-label">证件号码</view>
        <view class="form-value disable">
          {{formData.id_card}}
        </view>
      </view>

      <view class="form-item" wx:if="{{formData.vistor_type!='提货送货' && formData.status=='等待预约审批'}}">
        <view class="form-label">用餐:</view>
        <checkbox-group bindchange="bindChange" data-field="foods">
          <label wx:for="{{foods}}" wx:key="value">
            <checkbox value="{{item.value}}" checked="{{item.checked}}" />
            {{item.label}}
          </label>
        </checkbox-group>
      </view>

      <view class="form-item" wx:if="{{formData.vistor_type=='提货送货' && formData.fstate!=='3' && formData.fstate!=='0'}}">
        <view class="form-label">劳保物品</view>
        <view class="form-value">
          <checkbox-group class="ppe-checkbox-group" bindchange="bindChange" data-field="ppes">
            <label class="ppe-checkbox-label" wx:for="{{ppes}}" wx:key="value">
              <checkbox class="small07" value="{{item.value}}" checked="{{item.checked}}" />{{item.label}}
            </label>
          </checkbox-group>
        </view>
      </view>
      <view class="form-item" wx:if="{{formData.vistor_type=='提货送货' && formData.fstate=='3' }}">
        <view class="form-label">归还</view>
        <view class="form-value">
          <label>
            <switch class="small07" type="checkbox" checked="{{formData.is_ppe}}" bindchange="bindChange" data-field="is_ppe" />
            <text>已归还</text>
          </label>
          <view>
            <text style="margin: 0 10rpx;" wx:for="{{formData.ppes}}" wx:key="*this">{{item}}</text>
          </view>
        </view>
      </view>



    </view>

    <view style="display: flex;flex-direction: column;align-items: center;margin:20rpx;gap: 20rpx;">
      <text style="margin-bottom: -10rpx;font-size: 28rpx;">通行证</text>
      <image class="qrcode" src="{{qrCodeUrl}}" data-type="qrcode" data-url="{{qrCodeUrl}}" bindtap="previewImage" />
      <text class="tag  {{(formData.status=='访客中'|| formData.status=='访客结束，请刷二维码出场'|| formData.status=='审批完成，等待进入')?'tag-green':'tag-red'}}" style="margin-top: -10rpx;"> {{(formData.status=='访客中'|| formData.status=='访客结束，请刷二维码出场' || formData.status=='审批完成，等待进入')?'允许通行':'不允许通行'}}</text>
    </view>

    <!-- 图片附件 -->
    <view class="section">
      <view class="section-title">上传附件</view>

      <view class="form-item">

        <view class="form-value">
          <view class="image-container">
            <!-- 已上传的图片 -->
            <view wx:for="{{formData.files}}" wx:key="*this" class="image-item">
              <image src="{{item}}" mode="aspectFill" bindtap="previewImage" data-url="{{item}}"></image>
              <view class="delete-btn" bindtap="deleteImage" data-index="{{index}}">
                <icon type="clear"></icon>
              </view>
            </view>

            <!-- 上传按钮 -->
            <view class="upload-btn" bindtap="uploadImage">
              <text class="iconfont icon-add"></text>
              <text class="upload-text">上传图片</text>
            </view>
          </view>
          <view class="tips">最多上传5张，单张不超过2MB</view>
        </view>
      </view>
    </view>

  </form>

  <!-- 发起区域变更按钮（状态为访客中时显示） -->
  <!-- <view wx:if="{{formData.is_can_region && is_region=='2'}}" style="text-align: center; margin: 20rpx;">
    <button bindtap="initiateRegionChange">发起区域变更</button>
  </view> -->
</view>


<!--底部栏-->
<view class='bottomTabBar comTabBar' wx:if="{{ formData.fstate<2 }}">
  <view class='oneTabBarTouch' bindtap='goPages' data-pages="vt_yuyue_list">
    <image src='../../icon/home.png'></image>
    返回审批
  </view>
  <view class='oneTabBarTouch' bindtap='goUpload' wx:if="{{formData.fstate=='0' || formData.fstate=='2'}}" data-type="0">
    <image src='../../icon/sp.png'></image>
    审批
  </view>
  <view class='oneTabBarTouch' bindtap='goUpload' wx:if="{{formData.fstate=='1' || formData.fstate=='3'}}" data-type="1">
    <image src='../../icon/delete.png'></image>
    回收
  </view>
  <view class='oneTabBarTouch' bindtap='submitForm'>
    <image src='../../icon/submit.png'></image>
    修改
  </view>
</view>

<view class='bottomTabBar comTabBar' wx:if="{{ formData.fstate>=2 }}">
  <view class='oneTabBarTouch' bindtap='goPages' data-pages="vt_yuyue_list">
    <image src='../../icon/home.png'></image>
    返回审批
  </view>
  <view class='oneTabBarTouch' bindtap='goUpload' wx:if="{{formData.fstate=='0' || formData.fstate=='2'}}" data-type="0">
    <image src='../../icon/sp.png'></image>
    审批
  </view>
  <view class='oneTabBarTouch' bindtap='goUpload' wx:if="{{formData.fstate=='1' || formData.fstate=='3'}}" data-type="1">
    <image src='../../icon/delete.png'></image>
    回收
  </view>
  <view class='oneTabBarTouch' bindtap='initiateRegionChange' wx:if="{{formData.is_can_region && is_region=='2'}}">
    <image src='../../icon/submit.png'></image>
    区域变更
  </view>
  <view class='oneTabBarTouch' bindtap='submitForm' wx:if="{{!formData.is_can_region || is_region!='2'}}">
    <image src='../../icon/submit.png'></image>
    修改
  </view>
  <view class='oneTabBarTouch' bindtap='endVisit'>
    <image src='../../icon/cancel.png'></image>
    结束
  </view>
</view>
<!--底部栏-->