# 稳定推流管理器

这是一个改进版的RTSP到RTMP推流管理器，相比原版本具有以下优势：

## 主要改进

### 1. 更好的进程管理
- 使用专门的进程管理类，避免僵尸进程
- 优雅的进程启动和停止机制
- 进程异常时自动清理资源

### 2. 智能重连机制
- 区分网络错误和推流错误
- 指数退避重试策略
- 可配置的最大重试次数和延迟

### 3. 完善的监控系统
- 实时监控每个推流的状态
- 系统资源使用率监控
- 详细的日志记录和错误追踪

### 4. 灵活的配置管理
- JSON配置文件，易于修改
- 支持动态添加/删除摄像头
- 每个摄像头独立配置重试参数

### 5. 友好的控制界面
- 命令行工具，方便操作
- 实时状态显示
- 支持单独重启某个推流

### 6. 现有进程检测
- 自动检测已运行的ffmpeg推流进程
- 兼容原有脚本启动的进程
- 可以管理和停止外部进程

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 启动所有推流
```bash
python stream_control.py start
```

### 2. 查看推流状态
```bash
python stream_control.py status
```

### 3. 实时监控推流
```bash
python stream_control.py monitor
# 或指定刷新间隔（秒）
python stream_control.py monitor --interval 10
```

### 4. 重启指定推流
```bash
python stream_control.py restart --stream Camera_118
```

### 5. 停止所有推流
```bash
python stream_control.py stop
```

### 6. 列出配置的摄像头
```bash
python stream_control.py list
```

### 7. 停止外部ffmpeg进程
```bash
python stream_control.py stop-external
```

### 8. 测试进程检测功能
```bash
python quick_test.py
```

## 配置文件

首次运行时会自动创建 `stream_config.json` 配置文件，包含原脚本的所有摄像头配置。

配置文件格式：
```json
{
  "cameras": [
    {
      "ip": "***************",
      "rtsp_url": "rtsp://admin:aa987321@***************:554/h264/ch1/sub/av_stream",
      "rtmp_url": "rtmp://************:8053/118/001",
      "name": "Camera_118",
      "max_retries": 5,
      "retry_delay": 10
    }
  ]
}
```

## 日志文件

程序运行时会生成 `stream_manager.log` 日志文件，记录：
- 推流启动/停止事件
- 错误信息和重试记录
- 系统资源使用情况
- 网络连接状态

## 推流状态说明

- **stopped**: 已停止
- **starting**: 启动中
- **running**: 正常运行
- **error**: 出现错误
- **reconnecting**: 重连中

## 故障排除

### 1. 推流频繁重启
- 检查网络连接是否稳定
- 确认RTSP地址和认证信息正确
- 检查RTMP服务器是否正常

### 2. 内存使用过高
- 减少同时运行的推流数量
- 调整ffmpeg参数降低码率
- 检查是否有进程泄漏

### 3. 推流质量问题
- 调整ffmpeg编码参数
- 检查网络带宽是否足够
- 考虑降低分辨率或帧率

## 高级配置

### FFmpeg参数调优
可以修改 `StreamProcess._build_ffmpeg_command()` 方法中的参数：

- `-b:v 500k`: 视频码率
- `-r 15`: 帧率
- `-preset veryfast`: 编码速度预设
- `scale='min(720,iw)':-2`: 视频缩放

### 监控间隔调整
- 进程监控间隔：修改 `_monitor_process()` 中的 `time.sleep(10)`
- 状态报告间隔：修改 `_status_monitor()` 中的 `time.sleep(60)`

## 与原版本对比

| 功能 | 原版本 | 新版本 |
|------|--------|--------|
| 进程管理 | 简单subprocess | 专业进程管理类 |
| 错误处理 | 基础重试 | 智能重连机制 |
| 监控能力 | 基础网络检查 | 全面状态监控 |
| 日志记录 | 简单输出 | 结构化日志 |
| 配置管理 | 硬编码 | JSON配置文件 |
| 控制界面 | 无 | 命令行工具 |
| 资源管理 | 可能泄漏 | 自动清理 |

## 注意事项

1. 确保FFmpeg已正确安装并在PATH中
2. 首次运行前检查网络连接和摄像头状态
3. 定期检查日志文件，及时发现问题
4. 在生产环境中建议使用系统服务方式运行

## 后续改进建议

1. 添加Web管理界面
2. 支持推流质量自动调节
3. 集成告警通知功能
4. 支持负载均衡和故障转移
