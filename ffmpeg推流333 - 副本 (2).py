﻿import subprocess
import multiprocessing 
import retrying
import socket
import time
import os

ip_s = ["***************", "**************", "*************", "**************", "**************", "**************",
        "**************", "**************","**************", "***************", "**************", "***************", "***************",
        "**************", "**************", "***************","*************", "*************", "*************", "**************",
        "**************", "**************", "**************"]
ip_m=["118","19","6","12","16","24","28","29","30","117","31","124","126","18","21","144","1405","1406","1409","14010","14020","14023","14034"]

def server_url(r_i):
    url_parts = ["rtmp://************:8053/", r_i, "/001"]
    url = "".join(url_parts)
    return url

def should_retry(exception):
    if isinstance(exception, Exception) and ("网络已经断开" in str(exception) or "推流失败" in str(exception)):
        return True
    else:
        return False

@retrying.retry(wait_fixed=5000, retry_on_exception=should_retry)
def stream(rtsp_url, rtmp_url, device_index):
    ffmpeg_cmd = f"ffmpeg -rtsp_transport tcp -thread_queue_size 128 -i {rtsp_url} -an -c:v h264 -preset:v veryfast -tune zerolatency -b:v 500k -maxrate:v 500k -bufsize:v 500k -g 30 -f flv -vf \"scale = 'min(720,iw)':-2\" -r 15 -threads 0 -profile:v high -flags:v +cgop -pix_fmt yuv420p -movflags +faststart -fflags +genpts -streaming 1 {rtmp_url}"
    with open('output.txt', 'a') as file:
        file.write(ffmpeg_cmd+'\n')
    process = subprocess.Popen(ffmpeg_cmd, shell=True, cwd=".", creationflags=subprocess.CREATE_NEW_PROCESS_GROUP)  

    while process.poll() is None:
        # 检查网络连接状态
        try:
            socket.create_connection((ip_s[device_index], 554))
        except OSError:
            print(f"设备{device_index}网络已经断开")
            os.killpg(process.pid, subprocess.SIGTERM)  # 终止进程组
            raise Exception(f"设备{device_index}网络已经断开")

        time.sleep(5)  

    if process.returncode != 0:
        print(f"设备{device_index}推流失败:{rtsp_url} -> {rtmp_url}")
        raise Exception(f"设备{device_index}推流失败")
    else:
        print(f"{rtsp_url} 推流成功")

if __name__ == '__main__':
    rtsp_urls = ["rtsp://admin:aa987321@***************:554/h264/ch1/sub/av_stream", 
                 "rtsp://admin:aa987321@**************:554/h264/ch1/sub/av_stream", 
                 "rtsp://admin:aa987321@*************:554/h264/ch1/sub/av_stream", 
                 "rtsp://admin:aa987321@**************:554/h264/ch1/sub/av_stream", 
                 "rtsp://admin:aa987321@**************:554/h264/ch1/sub/av_stream", 
                 "rtsp://admin:aa987321@**************:554/h264/ch1/sub/av_stream", 
                 "rtsp://admin:aa987321@**************:554/h264/ch1/sub/av_stream", 
                 "rtsp://admin:aa987321@**************:554/h264/ch1/sub/av_stream", 
                 "rtsp://admin:aa987321@**************:554/h264/ch1/sub/av_stream", 
                 "rtsp://admin:aa987321@***************:554/h264/ch1/sub/av_stream", 
                 "rtsp://admin:aa987321@**************:554/h264/ch1/sub/av_stream", 
                 "rtsp://admin:aa987321@***************:554/h264/ch1/sub/av_stream", 
                 "rtsp://admin:aa987321@***************:554/h264/ch1/sub/av_stream", 
                 "rtsp://admin:aa987321@**************:554/h264/ch1/sub/av_stream", 
                 "rtsp://admin:aa987321@**************:554/h264/ch1/sub/av_stream", 
                 "rtsp://admin:aa987321@***************:554/h264/ch1/sub/av_stream", 
                 "rtsp://admin:aa987321@*************:554/h264/ch1/sub/av_stream", 
                 "rtsp://admin:aa987321@*************:554/h264/ch1/sub/av_stream", 
                 "rtsp://admin:aa987321@*************:554/h264/ch1/sub/av_stream", 
                 "rtsp://admin:aa987321@**************:554/h264/ch1/sub/av_stream", 
                 "rtsp://admin:aa987321@**************:554/h264/ch1/sub/av_stream", 
                 "rtsp://admin:aa987321@**************:554/h264/ch1/sub/av_stream", 
                 "rtsp://admin:aa987321@**************:554/h264/ch1/sub/av_stream"]
    rtmp_urls = [server_url(str(i)) for i in ip_m]

    processes = []
    for i, (rtsp_url, rtmp_url) in enumerate(zip(rtsp_urls, rtmp_urls)):
        process = multiprocessing.Process(target=stream, args=(rtsp_url, rtmp_url, i), daemon=True)
        process.start()
        processes.append(process)

    for process in processes:
        process.join()
    
    time.sleep(5)  # 脚本退出前休眠5秒