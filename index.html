<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title>通行证</title>
    <link type="text/css" rel="stylesheet" href="assets/index.css" />
    <link type="text/css" rel="stylesheet" href="assets/public.css" />
    <meta name="viewport"
          content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <script type="text/javascript">
        (function setFontSize() {
            if (document.documentElement.clientWidth === 0) {
                setTimeout(setFontSize, 100);
                return;
            }
            document.documentElement.style.fontSize = document.documentElement.clientWidth / 14.5 + 'px'
        })();
    </script>
    <style type="text/css">
        .wrapper {
            width: 100%;
            height: 100%;
        }

        .title {
            height: 1.5rem;
            line-height: 1.5rem;
            font-size: 0.65rem;
            text-align: center;
            color: white;
        }

        .content {
            height: calc(100% - 1.8rem);
            overflow: auto;
        }

        .row {
            width: 90%;
            margin: 0 auto;
            border: 1px solid #91d0f4;
            border-radius: 0.4rem;
            background: white;
            padding: 0.2rem 0.4rem;
            position: relative;
            margin-top: 0.3rem;
        }

            .row span {
                font-size: 0.5rem;
                width: 100%;
                float: left;
                padding: 0.2rem 0;
            }

        .clearfix {
            clear: both;
        }

        body {
            background: linear-gradient(180deg, #1778dc, #7fc9f2);
        }

        .icon {
            position: absolute;
            top: 1.5rem;
            right: 0.3rem;
            width: auto !important;
        }

        .back {
            position: absolute;
            left: 0.2rem;
            top: 0.4rem;
        }

        .no-data {
            font-size: 0.5rem;
            text-align: center;
            line-height: 1rem;
        }

        .qrcode-container {
            width: 90%;
            margin: 0 auto;
            border-radius: 0.3rem;
            background: white;
            text-align: center;
            margin-top: 0.2rem;
        }

        .info {
            background: #9aabb6;
            border-top-right-radius: 0.3rem;
            border-top-left-radius: 0.3rem;
            font-size: 0.7rem;
            color: white;
            padding: 0.5rem 0;
        }

        .qrcode {
            padding-top: 0.5rem;
        }

            .qrcode img {
                width: 10rem;
            }

        .status {
            font-size: 0.6rem;
            padding: 0.3rem 0;
        }

        .status2 {
            font-size: 0.8rem;
        }

            .status2.green {
                color: green;
            }

            .status2.red {
                color: #9aabb6;
            }

        .el-icon-circle-check {
            color: green;
        }

        .detail {
            width: 90%;
            margin: 0 auto;
        }

            .detail div {
                width: 48%;
                font-size: 0.65rem;
                background: white;
                border-radius: 0.3rem;
                float: left;
                padding: 0.2rem;
                box-sizing: border-box;
                margin-top: 0.5rem;
                height: 60px;
            }

                .detail div span {
                    width: 100%;
                    float: left;
                    padding: 0 0.2rem;
                    box-sizing: border-box;
                }

        .descr {
            color: #666;
        }

        .value {
            color: #057dc1;
        }
 
        /* 新增对话框样式 */
        .dialog-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 999;
        }

        .dialog {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 0.8rem;
            border-radius: 0.5rem;
            z-index: 1000;
            width: 85%;
            max-width: 15rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

            .dialog h2 {
                font-size: 0.7rem;
                margin: 0 0 0.5rem;
                text-align: center;
                color: #333;
                font-weight: 600;
            }

            .dialog select {
                width: 100%;
                padding: 0.4rem;
                font-size: 0.55rem;
                border: 2px solid #e1e5e9;
                border-radius: 0.3rem;
                margin-bottom: 0.6rem;
                background: white;
                transition: border-color 0.3s ease;
                box-sizing: border-box;
            }

            .dialog select:focus {
                outline: none;
                border-color: #1778dc;
            }

            .dialog .button-group {
                display: flex;
                gap: 0.3rem;
                justify-content: center;
            }

            .dialog .button {
                flex: 1;
                padding: 0.4rem 0.6rem;
                font-size: 0.55rem;
                border: none;
                border-radius: 0.3rem;
                cursor: pointer;
                transition: all 0.3s ease;
                font-weight: 500;
            }

            .dialog .button.primary {
                background: linear-gradient(135deg, #1778dc, #4a9eff);
                color: white;
            }

            .dialog .button.primary:hover {
                background: linear-gradient(135deg, #0f5cb8, #3a8eef);
                transform: translateY(-1px);
                box-shadow: 0 2px 8px rgba(23, 120, 220, 0.3);
            }

            .dialog .button.secondary {
                background: #f8f9fa;
                color: #6c757d;
                border: 1px solid #dee2e6;
            }

            .dialog .button.secondary:hover {
                background: #e9ecef;
                color: #495057;
            }

        /* 区域变更按钮样式优化 */
        .region-change-btn {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 0.5rem 1.2rem;
            font-size: 0.6rem;
            border-radius: 0.4rem;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            box-shadow: 0 2px 8px rgba(40, 167, 69, 0.2);
        }

        .region-change-btn:hover {
            background: linear-gradient(135deg, #218838, #1ea085);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }

        .region-change-btn:active {
            transform: translateY(0);
            box-shadow: 0 2px 8px rgba(40, 167, 69, 0.2);
        }
    </style> 
</head> 
<body> 
    <div id="app">
        <div class="wrapper">
            <div class="title">
                <span class="back el-icon-back" onclick="history.go(-1)"></span>
                通行证
            </div>
            <div class="content">
                <div class="qrcode-container">
                    <div class="info" v-html="welcomeStr"></div>
                    <div class="qrcode">
                        <img :src="qrCodeUrl" />
                    </div>
                    <div :class="apply.allow?'status2 green':'status2 red'">
                        <span :class="apply.allow?'el-icon-circle-check':'el-icon-circle-close'"></span>
                        {{apply.allow?"允许通行":"不允许通行"}}
                    </div>
                    <div class="status" style="padding-bottom:0;">
                        {{apply.status}}
                    </div>
                    <div class="status">
                        {{apply.time}}
                    </div>
                </div>
                <div class="detail">
                    <div>
                        <span class="descr">访客类型</span>
                        <span class="value">{{apply.vistor_type}}</span>
                    </div>
                    <div style="margin-left:4%;">
                        <span class="descr">受访人</span>
                        <span class="value">{{apply.empname||apply.empno}}</span>
                    </div>
                    <div>
                        <span class="descr">车牌号</span>
                        <span class="value">{{apply.carno==""?"&nbsp;":apply.carno}}</span>
                    </div>
                    <div style="margin-left:4%;">
                        <span class="descr">随访人数</span>
                        <span class="value">{{apply.num}}</span>
                    </div>
                    <!-- 区域变更申请按钮，仅当 apply.fstate 为 3 时显示 -->
                    <div v-if="apply.fstate == 3" style="width: 100%; text-align: center; margin-top: 0.8rem;">
                        <button class="region-change-btn" @click="openRegionChangeDialog">发起区域变更申请</button>
                    </div>
                </div>
            </div>
            <!-- 对话框遮罩 -->
            <div class="dialog-overlay" v-if="showDialog" @click="closeDialog"></div>
            <!-- 区域变更对话框 -->
            <div class="dialog" v-if="showDialog">
                <h2>选择区域</h2>
                <select v-model="selectedRegion">
                    <option value="">请选择区域</option>
                    <option v-for="region in regions" :key="region" :value="region">{{region}}</option>
                </select>
                <div class="button-group">
                    <button class="button secondary" @click="closeDialog">取消</button>
                    <button class="button primary" @click="submitRegionChange">确定</button>
                </div>
            </div>
        </div>
    </div>
    <script type="text/javascript" src="assets/vue.js"></script>
    <script type="text/javascript" src="assets/vue-resource.min.js"></script>
    <script type="text/javascript" src="assets/index.js"></script>
    <script type="text/javascript" src="assets/jquery-3.4.1.min.js"></script>
    <script type="text/javascript" src="assets/public.js"></script>
    <script type="text/javascript">

        function getQueryString(name) {
            const reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            const r = window.location.search.substr(1).match(reg);
            return r ? decodeURIComponent(r[2]) : null;
        }

        new Vue({
            el: '#app',
            data: function () {
                return {
                    id: getQueryString("id"),
                    welcomeStr: localStorage.getItem("USERNAME") + "，您好 欢迎来访<br />请向门卫出示二维码",
                    qrCodeUrl: "assets/img/qrcode.png",
                    apply: {},
                    showDialog: false,
                    regions: [],
                    selectedRegion: ''
                }
            },
            mounted: function () {
                if (this.id == "0") {
                    return;
                }
                this.query(true);
            },
            methods: {
                query: function (loading) {
                    if (loading) {
                        showLoading();
                    }
                    let _this = this;
                    _this.$http.get("/api/vistor/getapprovalinfo?id=" + this.id).then((res) => {
                        if (loading) {
                            hideLoading();
                        }
                        if (res.body.success) {
                            _this.apply = res.body.data;
                            _this.qrCodeUrl = res.body.data.qrCodeUrl || "assets/img/qrcode.png";
                        }
                        setTimeout(function () {
                            _this.query(false);
                        }, 5000);
                    });
                },
                openRegionChangeDialog: function () {
                    this.showDialog = true;
                    this.fetchRegions();
                },
                closeDialog: function () {
                    this.showDialog = false;
                    this.selectedRegion = '';
                    this.regions = [];
                },
                fetchRegions: function () {
                    this.$http.get('/wx/routeapi/all?ucode=P0001&csCode=Vis_region&api_zpj=getOptionsByCode').then((res) => {
                        if (res.bodyText) {
                            this.regions = res.bodyText.split(',');
                        } else {
                            console.log(res)
                            alert('无法获取区域列表，请稍后重试');
                        }
                    }).catch((error) => {
                        console.error('Error fetching regions:', error);
                        alert('无法获取区域列表，请稍后重试');
                    });
                },
                submitRegionChange: function () {
                    if (!this.selectedRegion) {
                        alert('请选择一个区域！');
                        return;
                    }
                    this.$http.post('http://127.0.0.1:8001/api/vistor/changeRegion', {
                        id: this.id,
                        region: this.selectedRegion
                    }).then((res) => {
                        if (res.body.status === 'success') {
                            alert('区域变更申请提交成功！');
                            this.closeDialog();
                        } else {
                            alert('提交失败：' + res.body.message);
                        }
                    }).catch((error) => {
                        console.error('Error submitting region change:', error);
                        alert('提交失败，请稍后重试');
                    });
                }
            }
        });
    </script>
</body>
</html>
