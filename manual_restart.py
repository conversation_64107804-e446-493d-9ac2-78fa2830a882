#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动重启失败的推流
"""

import json
import os
from datetime import datetime

def send_restart_command():
    """发送重启命令"""
    command_file = "stream_commands.json"
    
    command = {
        'type': 'restart_failed',
        'timestamp': datetime.now().isoformat()
    }
    
    # 读取现有命令
    commands = {'commands': []}
    if os.path.exists(command_file):
        with open(command_file, 'r', encoding='utf-8') as f:
            commands = json.load(f)
    
    # 添加新命令
    commands['commands'].append(command)
    
    # 保存命令
    with open(command_file, 'w', encoding='utf-8') as f:
        json.dump(commands, f, indent=2, ensure_ascii=False)
    
    print("✓ 重启命令已发送")
    print("主程序将在下次检查时（最多30秒内）处理此命令")

if __name__ == "__main__":
    send_restart_command()
