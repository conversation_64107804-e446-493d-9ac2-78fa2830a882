#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试脚本 - 验证推流管理器是否能检测现有进程
"""

from stable_stream_manager import StreamManager
import psutil
import re

def test_ffmpeg_detection():
    """测试ffmpeg进程检测功能"""
    print("=== FFmpeg进程检测测试 ===\n")
    
    # 1. 直接查找ffmpeg进程
    print("1. 查找系统中的ffmpeg进程:")
    ffmpeg_found = False
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if proc.info['name'] and 'ffmpeg' in proc.info['name'].lower():
                cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                print(f"   PID: {proc.info['pid']}")
                print(f"   命令: {cmdline[:100]}...")
                
                # 检查是否是推流命令
                if 'rtsp://' in cmdline and 'rtmp://' in cmdline:
                    ip_match = re.search(r'rtsp://[^@]*@(\d+\.\d+\.\d+\.\d+):', cmdline)
                    if ip_match:
                        print(f"   源IP: {ip_match.group(1)}")
                        ffmpeg_found = True
                print()
                
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            continue
    
    if not ffmpeg_found:
        print("   未找到ffmpeg推流进程")
    
    print("\n" + "="*50)
    
    # 2. 测试StreamManager的检测功能
    print("2. 测试StreamManager检测功能:")
    manager = StreamManager()
    
    # 获取状态
    status_list = manager.get_all_status()
    
    if status_list:
        print(f"   检测到 {len(status_list)} 个推流:")
        for status in status_list:
            managed = "管理的" if status.get('managed', True) else "外部"
            print(f"   - {status['name']} ({status['ip']}) - {managed}进程, PID: {status['pid']}")
    else:
        print("   未检测到推流进程")
    
    print("\n" + "="*50)
    
    # 3. 测试find_ffmpeg_processes方法
    print("3. 测试find_ffmpeg_processes方法:")
    processes = manager.find_ffmpeg_processes()
    
    if processes:
        print(f"   找到 {len(processes)} 个ffmpeg推流进程:")
        for proc in processes:
            print(f"   - PID: {proc['pid']}, 源IP: {proc['source_ip']}, RTMP: {proc['rtmp_server']}")
    else:
        print("   未找到ffmpeg推流进程")

if __name__ == "__main__":
    test_ffmpeg_detection()
