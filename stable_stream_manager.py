#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
稳定的RTSP到RTMP推流管理器
改进版本，具有更好的错误处理、进程管理和监控功能
"""

import subprocess
import threading
import time
import socket
import logging
import json
import os
import signal
import psutil
import re
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import queue

class StreamStatus(Enum):
    """推流状态枚举"""
    STOPPED = "stopped"
    STARTING = "starting"
    RUNNING = "running"
    ERROR = "error"
    RECONNECTING = "reconnecting"

@dataclass
class CameraConfig:
    """摄像头配置"""
    ip: str
    rtsp_url: str
    rtmp_url: str
    name: str
    max_retries: int = 5
    retry_delay: int = 10

@dataclass
class StreamStats:
    """推流统计信息"""
    start_time: Optional[datetime] = None
    last_restart_time: Optional[datetime] = None
    restart_count: int = 0
    error_count: int = 0
    status: StreamStatus = StreamStatus.STOPPED
    last_error: str = ""

class StreamProcess:
    """单个推流进程管理器"""
    
    def __init__(self, camera_config: CameraConfig, logger: logging.Logger):
        self.config = camera_config
        self.logger = logger
        self.process: Optional[subprocess.Popen] = None
        self.stats = StreamStats()
        self.should_stop = threading.Event()
        self.monitor_thread: Optional[threading.Thread] = None
        self._lock = threading.Lock()
        
    def start(self) -> bool:
        """启动推流进程"""
        with self._lock:
            if self.stats.status == StreamStatus.RUNNING:
                return True
                
            self.stats.status = StreamStatus.STARTING
            self.logger.info(f"启动推流: {self.config.name} ({self.config.ip})")
            
            try:
                # 构建ffmpeg命令
                ffmpeg_cmd = self._build_ffmpeg_command()
                
                # 启动进程
                self.process = subprocess.Popen(
                    ffmpeg_cmd,
                    shell=True,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    creationflags=subprocess.CREATE_NEW_PROCESS_GROUP if os.name == 'nt' else 0
                )
                
                self.stats.start_time = datetime.now()
                self.stats.status = StreamStatus.RUNNING
                
                # 启动监控线程
                self.monitor_thread = threading.Thread(target=self._monitor_process, daemon=True)
                self.monitor_thread.start()
                
                self.logger.info(f"推流启动成功: {self.config.name}, PID: {self.process.pid}")
                return True
                
            except Exception as e:
                self.stats.status = StreamStatus.ERROR
                self.stats.last_error = str(e)
                self.stats.error_count += 1
                self.logger.error(f"启动推流失败: {self.config.name}, 错误: {e}")
                return False
    
    def stop(self):
        """停止推流进程"""
        with self._lock:
            self.should_stop.set()
            self.stats.status = StreamStatus.STOPPED
            
            if self.process:
                try:
                    # 优雅关闭
                    if os.name == 'nt':
                        self.process.send_signal(signal.CTRL_BREAK_EVENT)
                    else:
                        self.process.terminate()
                    
                    # 等待进程结束
                    try:
                        self.process.wait(timeout=5)
                    except subprocess.TimeoutExpired:
                        # 强制杀死
                        self.process.kill()
                        self.process.wait()
                        
                    self.logger.info(f"推流已停止: {self.config.name}")
                    
                except Exception as e:
                    self.logger.error(f"停止推流时出错: {self.config.name}, 错误: {e}")
                finally:
                    self.process = None
    
    def restart(self) -> bool:
        """重启推流"""
        self.logger.info(f"重启推流: {self.config.name}")
        self.stop()
        time.sleep(2)  # 等待资源释放
        
        self.stats.last_restart_time = datetime.now()
        self.stats.restart_count += 1
        
        return self.start()
    
    def _build_ffmpeg_command(self) -> str:
        """构建ffmpeg命令"""
        return (
            f"ffmpeg -y "
            f"-rtsp_transport tcp "
            f"-thread_queue_size 512 "
            f"-i \"{self.config.rtsp_url}\" "
            f"-c:v libx264 "
            f"-preset veryfast "
            f"-tune zerolatency "
            f"-b:v 500k "
            f"-maxrate 500k "
            f"-bufsize 1000k "
            f"-g 30 "
            f"-r 15 "
            f"-vf \"scale='min(720,iw)':-2\" "
            f"-an "
            f"-f flv "
            f"-reconnect 1 "
            f"-reconnect_at_eof 1 "
            f"-reconnect_streamed 1 "
            f"-reconnect_delay_max 5 "
            f"\"{self.config.rtmp_url}\" "
            f"-loglevel error"
        )
    
    def _monitor_process(self):
        """监控进程状态"""
        consecutive_failures = 0
        
        while not self.should_stop.is_set():
            try:
                if not self.process or self.process.poll() is not None:
                    # 进程已结束
                    if not self.should_stop.is_set():
                        self.stats.status = StreamStatus.ERROR
                        self.stats.error_count += 1
                        consecutive_failures += 1
                        
                        self.logger.warning(f"推流进程异常结束: {self.config.name}, 连续失败次数: {consecutive_failures}")
                        
                        if consecutive_failures <= self.config.max_retries:
                            self.stats.status = StreamStatus.RECONNECTING
                            time.sleep(self.config.retry_delay)
                            
                            if self._check_network_connectivity():
                                self.logger.info(f"尝试重启推流: {self.config.name}")
                                if self.restart():
                                    consecutive_failures = 0
                            else:
                                self.logger.warning(f"网络不通，跳过重启: {self.config.name}")
                        else:
                            self.logger.error(f"推流重试次数超限，交由主监控线程处理: {self.config.name}")
                            self.stats.status = StreamStatus.STOPPED
                            # 不要break，让主监控线程接管重启逻辑
                            time.sleep(30)  # 等待一段时间再继续监控
                else:
                    # 进程正常运行
                    consecutive_failures = 0
                    self.stats.status = StreamStatus.RUNNING
                
                time.sleep(10)  # 检查间隔
                
            except Exception as e:
                self.logger.error(f"监控进程时出错: {self.config.name}, 错误: {e}")
                time.sleep(5)
    
    def _check_network_connectivity(self) -> bool:
        """检查网络连通性"""
        try:
            sock = socket.create_connection((self.config.ip, 554), timeout=5)
            sock.close()
            return True
        except (socket.error, socket.timeout):
            return False
    
    def get_status(self) -> Dict:
        """获取状态信息"""
        return {
            "name": self.config.name,
            "ip": self.config.ip,
            "status": self.stats.status.value,
            "start_time": self.stats.start_time.isoformat() if self.stats.start_time else None,
            "restart_count": self.stats.restart_count,
            "error_count": self.stats.error_count,
            "last_error": self.stats.last_error,
            "pid": self.process.pid if self.process else None
        }

class StreamManager:
    """推流管理器主类"""

    def __init__(self, config_file: str = "stream_config.json"):
        self.config_file = config_file
        self.status_file = "stream_status.json"  # 状态文件
        self.command_file = "stream_commands.json"  # 命令文件
        self.streams: Dict[str, StreamProcess] = {}
        self.logger = self._setup_logger()
        self.should_stop = threading.Event()
        self.status_monitor_thread: Optional[threading.Thread] = None

        # 启动时检测现有的ffmpeg进程
        self._detect_existing_streams()
        
    def _setup_logger(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger("StreamManager")
        logger.setLevel(logging.INFO)
        
        # 文件处理器
        file_handler = logging.FileHandler("stream_manager.log", encoding='utf-8')
        file_handler.setLevel(logging.INFO)
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # 格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        return logger

    def _detect_existing_streams(self):
        """检测现有的ffmpeg推流进程"""
        try:
            cameras = self.load_config()
            camera_dict = {cam.ip: cam for cam in cameras}

            # 查找所有ffmpeg进程
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['name'] and 'ffmpeg' in proc.info['name'].lower():
                        cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''

                        # 解析命令行，提取IP地址
                        ip_match = re.search(r'rtsp://[^@]*@(\d+\.\d+\.\d+\.\d+):', cmdline)
                        if ip_match:
                            ip = ip_match.group(1)

                            # 如果这个IP在我们的配置中
                            if ip in camera_dict:
                                camera = camera_dict[ip]

                                # 创建StreamProcess对象来管理这个现有进程
                                stream = StreamProcess(camera, self.logger)

                                # 创建一个模拟的进程对象
                                real_process = psutil.Process(proc.info['pid'])

                                class MockProcess:
                                    def __init__(self, pid, real_proc):
                                        self.pid = pid
                                        self._real_proc = real_proc

                                    def poll(self):
                                        try:
                                            if self._real_proc.is_running():
                                                return None  # 进程还在运行
                                            else:
                                                return 0  # 进程已结束
                                        except psutil.NoSuchProcess:
                                            return 1  # 进程不存在

                                    def terminate(self):
                                        try:
                                            self._real_proc.terminate()
                                        except psutil.NoSuchProcess:
                                            pass

                                    def kill(self):
                                        try:
                                            self._real_proc.kill()
                                        except psutil.NoSuchProcess:
                                            pass

                                    def wait(self, timeout=None):
                                        try:
                                            return self._real_proc.wait(timeout)
                                        except psutil.NoSuchProcess:
                                            return 0

                                    def send_signal(self, sig):
                                        try:
                                            self._real_proc.send_signal(sig)
                                        except psutil.NoSuchProcess:
                                            pass

                                stream.process = MockProcess(proc.info['pid'], real_process)

                                stream.stats.status = StreamStatus.RUNNING
                                stream.stats.start_time = datetime.fromtimestamp(
                                    psutil.Process(proc.info['pid']).create_time()
                                )

                                self.streams[camera.name] = stream
                                self.logger.info(f"检测到现有推流进程: {camera.name} (PID: {proc.info['pid']})")

                                # 启动监控线程
                                stream.monitor_thread = threading.Thread(
                                    target=stream._monitor_process, daemon=True
                                )
                                stream.monitor_thread.start()

                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue

        except Exception as e:
            self.logger.error(f"检测现有推流进程时出错: {e}")

    def find_ffmpeg_processes(self) -> List[Dict]:
        """查找所有ffmpeg推流进程"""
        processes = []
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'create_time']):
                try:
                    if proc.info['name'] and 'ffmpeg' in proc.info['name'].lower():
                        cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''

                        # 检查是否是推流命令（包含rtsp和rtmp）
                        if 'rtsp://' in cmdline and 'rtmp://' in cmdline:
                            # 提取IP地址
                            ip_match = re.search(r'rtsp://[^@]*@(\d+\.\d+\.\d+\.\d+):', cmdline)
                            rtmp_match = re.search(r'rtmp://([^/]+)', cmdline)

                            processes.append({
                                'pid': proc.info['pid'],
                                'cmdline': cmdline,
                                'source_ip': ip_match.group(1) if ip_match else 'Unknown',
                                'rtmp_server': rtmp_match.group(1) if rtmp_match else 'Unknown',
                                'create_time': datetime.fromtimestamp(proc.info['create_time']),
                                'status': 'running'
                            })

                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue

        except Exception as e:
            self.logger.error(f"查找ffmpeg进程时出错: {e}")

        return processes
    
    def load_config(self) -> List[CameraConfig]:
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                    return [CameraConfig(**cam) for cam in config_data['cameras']]
            else:
                # 创建默认配置
                return self._create_default_config()
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            return self._create_default_config()
    
    def _create_default_config(self) -> List[CameraConfig]:
        """创建默认配置（基于原脚本）"""
        cameras = []
        
        # 原脚本的IP和标识
        ip_list = ["***************", "**************", "*************", "**************", 
                  "**************", "**************", "**************", "**************",
                  "**************", "***************", "**************", "**************4", 
                  "**************6", "**************", "**************", "***************",
                  "*************", "*************", "*************", "**************",
                  "**************", "**************", "**************"]
        
        ip_m = ["118","19","6","12","16","24","28","29","30","117","31","124","126",
               "18","21","144","1405","1406","1409","14010","14020","14023","14034"]
        
        for i, (ip, stream_id) in enumerate(zip(ip_list, ip_m)):
            rtsp_url = f"rtsp://admin:aa987321@{ip}:554/h264/ch1/sub/av_stream"
            rtmp_url = f"rtmp://************:8053/{stream_id}/001"
            
            cameras.append(CameraConfig(
                ip=ip,
                rtsp_url=rtsp_url,
                rtmp_url=rtmp_url,
                name=f"Camera_{stream_id}",
                max_retries=5,
                retry_delay=10
            ))
        
        # 保存默认配置
        self.save_config(cameras)
        return cameras

    def save_config(self, cameras: List[CameraConfig]):
        """保存配置文件"""
        try:
            config_data = {
                "cameras": [
                    {
                        "ip": cam.ip,
                        "rtsp_url": cam.rtsp_url,
                        "rtmp_url": cam.rtmp_url,
                        "name": cam.name,
                        "max_retries": cam.max_retries,
                        "retry_delay": cam.retry_delay
                    }
                    for cam in cameras
                ]
            }

            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)

            self.logger.info(f"配置文件已保存: {self.config_file}")

        except Exception as e:
            self.logger.error(f"保存配置文件失败: {e}")

    def save_status(self):
        """保存当前状态到文件"""
        try:
            status_data = {
                "timestamp": datetime.now().isoformat(),
                "streams": {}
            }

            # 保存管理的推流状态
            for name, stream in self.streams.items():
                status_data["streams"][name] = {
                    "name": stream.config.name,
                    "ip": stream.config.ip,
                    "rtsp_url": stream.config.rtsp_url,
                    "rtmp_url": stream.config.rtmp_url,
                    "status": stream.stats.status.value,
                    "pid": stream.process.pid if stream.process else None,
                    "start_time": stream.stats.start_time.isoformat() if stream.stats.start_time else None,
                    "restart_count": stream.stats.restart_count,
                    "error_count": stream.stats.error_count,
                    "last_error": stream.stats.last_error,
                    "managed": True
                }

            with open(self.status_file, 'w', encoding='utf-8') as f:
                json.dump(status_data, f, indent=2, ensure_ascii=False)

        except Exception as e:
            self.logger.error(f"保存状态文件失败: {e}")

    def load_status(self) -> Dict:
        """从文件加载状态"""
        try:
            if os.path.exists(self.status_file):
                with open(self.status_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {"streams": {}}
        except Exception as e:
            self.logger.error(f"加载状态文件失败: {e}")
            return {"streams": {}}

    def start_all_streams(self):
        """启动所有推流"""
        cameras = self.load_config()

        self.logger.info(f"开始启动 {len(cameras)} 个推流")

        for camera in cameras:
            stream = StreamProcess(camera, self.logger)
            self.streams[camera.name] = stream

            # 启动推流
            if stream.start():
                self.logger.info(f"推流启动成功: {camera.name}")
            else:
                self.logger.error(f"推流启动失败: {camera.name}")

            # 间隔启动，避免同时启动太多进程
            time.sleep(1)

        # 启动状态监控
        self.status_monitor_thread = threading.Thread(target=self._status_monitor, daemon=True)
        self.status_monitor_thread.start()

        # 保存状态
        self.save_status()

        self.logger.info("所有推流启动完成")

    def stop_all_streams(self):
        """停止所有推流"""
        self.should_stop.set()

        self.logger.info("开始停止所有推流")

        for name, stream in self.streams.items():
            stream.stop()

        self.streams.clear()

        # 清除状态文件
        try:
            if os.path.exists(self.status_file):
                os.remove(self.status_file)
        except Exception as e:
            self.logger.error(f"删除状态文件失败: {e}")

        self.logger.info("所有推流已停止")

    def restart_stream(self, stream_name: str) -> bool:
        """重启指定推流"""
        if stream_name in self.streams:
            return self.streams[stream_name].restart()
        return False

    def get_all_status(self) -> List[Dict]:
        """获取所有推流状态"""
        status_list = []

        # 1. 添加当前管理的推流状态
        for stream in self.streams.values():
            status_list.append(stream.get_status())

        # 2. 从状态文件加载之前保存的状态
        saved_status = self.load_status()
        saved_streams = saved_status.get("streams", {})

        # 3. 获取当前运行的ffmpeg进程
        current_processes = self.find_ffmpeg_processes()
        current_pids = {proc['pid'] for proc in current_processes}

        # 4. 处理保存的状态
        for stream_name, saved_stream in saved_streams.items():
            # 检查这个流是否已经在当前管理的流中
            if stream_name not in [s.config.name for s in self.streams.values()]:
                # 检查进程是否还在运行
                saved_pid = saved_stream.get('pid')
                if saved_pid and saved_pid in current_pids:
                    # 进程还在运行，更新状态
                    saved_stream['status'] = 'running'
                    saved_stream['managed'] = True
                    status_list.append(saved_stream)
                else:
                    # 进程已停止
                    saved_stream['status'] = 'stopped'
                    saved_stream['pid'] = None
                    saved_stream['managed'] = True
                    status_list.append(saved_stream)

        # 5. 添加未被管理的外部进程
        managed_pids = set()
        for status in status_list:
            if status.get('pid'):
                managed_pids.add(status['pid'])

        for proc in current_processes:
            if proc['pid'] not in managed_pids:
                status_list.append({
                    "name": f"External_FFmpeg_{proc['source_ip']}",
                    "ip": proc['source_ip'],
                    "status": proc['status'],
                    "start_time": proc['create_time'].isoformat(),
                    "restart_count": 0,
                    "error_count": 0,
                    "last_error": "",
                    "pid": proc['pid'],
                    "rtmp_server": proc['rtmp_server'],
                    "managed": False  # 标记为非管理的进程
                })

        return status_list

    def _status_monitor(self):
        """状态监控线程"""
        while not self.should_stop.is_set():
            try:
                # 每分钟输出一次状态摘要
                running_count = sum(1 for s in self.streams.values()
                                  if s.stats.status == StreamStatus.RUNNING)
                error_count = sum(1 for s in self.streams.values()
                                if s.stats.status == StreamStatus.ERROR)
                reconnecting_count = sum(1 for s in self.streams.values()
                                       if s.stats.status == StreamStatus.RECONNECTING)

                self.logger.info(f"推流状态摘要 - 运行中: {running_count}, 错误: {error_count}, 重连中: {reconnecting_count}")

                # 检查并重启停止或错误的推流
                failed_streams = [s for s in self.streams.values()
                                if s.stats.status in [StreamStatus.STOPPED, StreamStatus.ERROR]]

                for stream in failed_streams:
                    # 检查网络连通性
                    if stream._check_network_connectivity():
                        self.logger.info(f"尝试重启失败的推流: {stream.config.name} (状态: {stream.stats.status.value})")
                        if stream.restart():
                            self.logger.info(f"推流重启成功: {stream.config.name}")
                        else:
                            self.logger.error(f"推流重启失败: {stream.config.name}")
                    else:
                        self.logger.warning(f"网络不通，跳过重启: {stream.config.name}")

                # 检查系统资源
                cpu_percent = psutil.cpu_percent()
                memory_percent = psutil.virtual_memory().percent

                if cpu_percent > 80 or memory_percent > 80:
                    self.logger.warning(f"系统资源使用率较高 - CPU: {cpu_percent}%, 内存: {memory_percent}%")

                # 检查并处理命令
                self._process_commands()

                # 保存状态
                self.save_status()

                time.sleep(30)  # 每30秒检查一次

            except Exception as e:
                self.logger.error(f"状态监控出错: {e}")
                time.sleep(10)

    def _process_commands(self):
        """处理外部命令"""
        try:
            if os.path.exists(self.command_file):
                with open(self.command_file, 'r', encoding='utf-8') as f:
                    commands = json.load(f)

                for command in commands.get('commands', []):
                    if command['type'] == 'restart_failed':
                        self.logger.info("收到重启失败推流的命令")
                        self._restart_failed_streams()
                    elif command['type'] == 'restart_stream':
                        stream_name = command.get('stream_name')
                        if stream_name:
                            self.logger.info(f"收到重启推流的命令: {stream_name}")
                            self.restart_stream(stream_name)

                # 处理完命令后删除文件
                os.remove(self.command_file)

        except Exception as e:
            self.logger.error(f"处理命令时出错: {e}")

    def _restart_failed_streams(self):
        """重启所有失败的推流"""
        failed_streams = [s for s in self.streams.values()
                         if s.stats.status in [StreamStatus.STOPPED, StreamStatus.ERROR]]

        if not failed_streams:
            self.logger.info("没有发现失败的推流")
            return

        self.logger.info(f"发现 {len(failed_streams)} 个失败的推流，开始重启...")

        restarted_count = 0
        for stream in failed_streams:
            if stream._check_network_connectivity():
                self.logger.info(f"重启失败的推流: {stream.config.name}")
                if stream.restart():
                    self.logger.info(f"推流重启成功: {stream.config.name}")
                    restarted_count += 1
                else:
                    self.logger.error(f"推流重启失败: {stream.config.name}")
            else:
                self.logger.warning(f"网络不通，跳过重启: {stream.config.name}")

        self.logger.info(f"重启完成，成功重启 {restarted_count}/{len(failed_streams)} 个推流")

    def send_command(self, command_type: str, **kwargs):
        """发送命令给运行中的管理器"""
        try:
            command = {
                'type': command_type,
                'timestamp': datetime.now().isoformat(),
                **kwargs
            }

            # 读取现有命令
            commands = {'commands': []}
            if os.path.exists(self.command_file):
                with open(self.command_file, 'r', encoding='utf-8') as f:
                    commands = json.load(f)

            # 添加新命令
            commands['commands'].append(command)

            # 保存命令
            with open(self.command_file, 'w', encoding='utf-8') as f:
                json.dump(commands, f, indent=2, ensure_ascii=False)

            return True

        except Exception as e:
            self.logger.error(f"发送命令失败: {e}")
            return False

    def run_forever(self):
        """持续运行"""
        try:
            self.start_all_streams()

            self.logger.info("推流管理器已启动，按 Ctrl+C 停止")

            while not self.should_stop.is_set():
                time.sleep(1)

        except KeyboardInterrupt:
            self.logger.info("收到停止信号")
        finally:
            self.stop_all_streams()

def main():
    """主函数"""
    manager = StreamManager()

    try:
        manager.run_forever()
    except Exception as e:
        manager.logger.error(f"程序异常退出: {e}")
    finally:
        manager.logger.info("程序已退出")

if __name__ == "__main__":
    main()
