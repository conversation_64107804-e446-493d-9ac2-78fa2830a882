#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
推流控制脚本
提供简单的命令行界面来管理推流
"""

import argparse
import json
import time
from stable_stream_manager import StreamManager, StreamStatus

def show_status(manager: StreamManager):
    """显示所有推流状态"""
    status_list = manager.get_all_status()

    if not status_list:
        print("没有检测到运行的推流进程")
        print("提示：如果有推流正在运行，请确保它们是通过ffmpeg启动的")
        return

    print("\n" + "="*100)
    print(f"{'名称':<20} {'IP地址':<15} {'状态':<12} {'重启次数':<8} {'错误次数':<8} {'PID':<8} {'管理状态':<10}")
    print("="*100)

    for status in status_list:
        managed = "已管理" if status.get('managed', True) else "外部进程"
        rtmp_info = f" -> {status.get('rtmp_server', 'N/A')}" if not status.get('managed', True) else ""

        print(f"{status['name']:<20} {status['ip']:<15} {status['status']:<12} "
              f"{status['restart_count']:<8} {status['error_count']:<8} {status['pid'] or 'N/A':<8} {managed:<10}")

        if rtmp_info:
            print(f"{'':>20} RTMP服务器: {status.get('rtmp_server', 'N/A')}")

    # 统计信息
    running = sum(1 for s in status_list if s['status'] == 'running')
    error = sum(1 for s in status_list if s['status'] == 'error')
    reconnecting = sum(1 for s in status_list if s['status'] == 'reconnecting')
    managed = sum(1 for s in status_list if s.get('managed', True))
    external = len(status_list) - managed

    print("="*100)
    print(f"总计: {len(status_list)} | 运行中: {running} | 错误: {error} | 重连中: {reconnecting}")
    print(f"管理的进程: {managed} | 外部进程: {external}")
    print("="*100)

    if external > 0:
        print("\n注意：检测到外部ffmpeg进程，这些进程不受此管理器控制")
        print("建议：停止外部进程后使用此管理器启动，以获得更好的管理功能")

def monitor_streams(manager: StreamManager, interval: int = 30):
    """持续监控推流状态"""
    print(f"开始监控推流状态，刷新间隔: {interval}秒")
    print("按 Ctrl+C 停止监控")
    
    try:
        while True:
            show_status(manager)
            time.sleep(interval)
    except KeyboardInterrupt:
        print("\n监控已停止")

def restart_stream(manager: StreamManager, stream_name: str):
    """重启指定推流"""
    if stream_name in manager.streams:
        print(f"正在重启推流: {stream_name}")
        if manager.restart_stream(stream_name):
            print(f"推流重启成功: {stream_name}")
        else:
            print(f"推流重启失败: {stream_name}")
    else:
        print(f"未找到推流: {stream_name}")
        print("可用的推流:")
        for name in manager.streams.keys():
            print(f"  - {name}")

def list_cameras():
    """列出配置的摄像头"""
    manager = StreamManager()
    cameras = manager.load_config()
    
    print("\n配置的摄像头:")
    print("="*60)
    for i, camera in enumerate(cameras, 1):
        print(f"{i:2d}. {camera.name}")
        print(f"    IP: {camera.ip}")
        print(f"    RTSP: {camera.rtsp_url}")
        print(f"    RTMP: {camera.rtmp_url}")
        print(f"    最大重试: {camera.max_retries}, 重试延迟: {camera.retry_delay}s")
        print()

def stop_external_ffmpeg():
    """停止所有外部ffmpeg推流进程"""
    import psutil
    import re

    stopped_count = 0

    try:
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if proc.info['name'] and 'ffmpeg' in proc.info['name'].lower():
                    cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''

                    # 检查是否是推流命令（包含rtsp和rtmp）
                    if 'rtsp://' in cmdline and 'rtmp://' in cmdline:
                        print(f"停止外部ffmpeg进程 PID: {proc.info['pid']}")

                        # 提取IP信息用于显示
                        ip_match = re.search(r'rtsp://[^@]*@(\d+\.\d+\.\d+\.\d+):', cmdline)
                        ip = ip_match.group(1) if ip_match else 'Unknown'
                        print(f"  源IP: {ip}")

                        # 终止进程
                        process = psutil.Process(proc.info['pid'])
                        process.terminate()

                        # 等待进程结束
                        try:
                            process.wait(timeout=5)
                            print(f"  ✓ 进程已停止")
                            stopped_count += 1
                        except psutil.TimeoutExpired:
                            # 强制杀死
                            process.kill()
                            process.wait()
                            print(f"  ✓ 进程已强制停止")
                            stopped_count += 1

            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                continue

    except Exception as e:
        print(f"停止外部进程时出错: {e}")

    print(f"\n总共停止了 {stopped_count} 个外部ffmpeg进程")

def restart_failed_streams(manager: StreamManager):
    """重启所有失败的推流"""
    status_list = manager.get_all_status()
    failed_streams = [s for s in status_list
                     if s['status'] in ['stopped', 'error'] and s.get('managed', True)]

    if not failed_streams:
        print("没有发现失败的推流")
        return

    print(f"发现 {len(failed_streams)} 个失败的推流")

    # 发送重启命令给运行中的管理器
    if manager.send_command('restart_failed'):
        print("✓ 重启命令已发送给运行中的推流管理器")
        print("请查看主程序日志了解重启进度")
    else:
        print("✗ 发送重启命令失败")

def main():
    parser = argparse.ArgumentParser(description="推流控制工具")
    parser.add_argument("command", choices=["start", "stop", "status", "monitor", "restart", "restart-failed", "list", "stop-external"],
                       help="执行的命令")
    parser.add_argument("--stream", "-s", help="指定推流名称（用于restart命令）")
    parser.add_argument("--interval", "-i", type=int, default=30,
                       help="监控刷新间隔（秒，默认30）")
    
    args = parser.parse_args()
    
    if args.command == "list":
        list_cameras()
        return
    
    manager = StreamManager()
    
    if args.command == "start":
        print("启动所有推流...")
        manager.start_all_streams()
        print("推流已启动，使用 'python stream_control.py status' 查看状态")
        
        # 持续运行
        try:
            print("推流管理器运行中，按 Ctrl+C 停止")
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n正在停止所有推流...")
            manager.stop_all_streams()
            print("所有推流已停止")
    
    elif args.command == "stop":
        print("停止所有推流...")
        manager.stop_all_streams()
        print("所有推流已停止")
    
    elif args.command == "status":
        show_status(manager)
    
    elif args.command == "monitor":
        monitor_streams(manager, args.interval)
    
    elif args.command == "restart":
        if not args.stream:
            print("请指定要重启的推流名称: --stream <name>")
            return
        restart_stream(manager, args.stream)

    elif args.command == "restart-failed":
        print("重启所有失败的推流...")
        restart_failed_streams(manager)

    elif args.command == "stop-external":
        print("停止所有外部ffmpeg推流进程...")
        stop_external_ffmpeg()
        print("外部进程停止完成")

    else:
        parser.print_help()

if __name__ == "__main__":
    main()
