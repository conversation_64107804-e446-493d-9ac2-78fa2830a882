#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试自动重启功能
"""

import time
import psutil
from stable_stream_manager import StreamManager

def kill_random_stream():
    """随机杀死一个推流进程来测试自动重启"""
    try:
        # 查找ffmpeg进程
        ffmpeg_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if proc.info['name'] and 'ffmpeg' in proc.info['name'].lower():
                    cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                    if 'rtsp://' in cmdline and 'rtmp://' in cmdline:
                        ffmpeg_processes.append(proc)
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                continue
        
        if ffmpeg_processes:
            # 杀死第一个找到的进程
            proc = ffmpeg_processes[0]
            print(f"杀死推流进程 PID: {proc.info['pid']}")
            proc.terminate()
            return True
        else:
            print("没有找到ffmpeg推流进程")
            return False
            
    except Exception as e:
        print(f"杀死进程时出错: {e}")
        return False

def main():
    print("测试自动重启功能...")
    
    manager = StreamManager()
    
    # 获取初始状态
    initial_status = manager.get_all_status()
    running_count = sum(1 for s in initial_status if s['status'] == 'running')
    
    print(f"初始运行的推流数量: {running_count}")
    
    if running_count == 0:
        print("没有运行的推流，请先启动推流")
        return
    
    # 杀死一个推流进程
    if kill_random_stream():
        print("已杀死一个推流进程，等待自动重启...")
        
        # 等待2分钟，观察自动重启
        for i in range(12):  # 12 * 10秒 = 2分钟
            time.sleep(10)
            status = manager.get_all_status()
            current_running = sum(1 for s in status if s['status'] == 'running')
            stopped = sum(1 for s in status if s['status'] == 'stopped')
            error = sum(1 for s in status if s['status'] == 'error')
            
            print(f"第{i+1}次检查 - 运行中: {current_running}, 停止: {stopped}, 错误: {error}")
            
            if current_running == running_count:
                print("✅ 自动重启成功！所有推流都已恢复运行")
                return
        
        print("❌ 自动重启可能失败，请检查日志")
    else:
        print("无法杀死推流进程进行测试")

if __name__ == "__main__":
    main()
