#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试状态检测功能
"""

from stable_stream_manager import StreamManager

def main():
    print("测试推流状态检测...")
    
    manager = StreamManager()
    
    # 获取状态
    status_list = manager.get_all_status()
    
    print(f"\n检测到 {len(status_list)} 个推流:")
    print("="*80)
    
    for status in status_list:
        managed = "管理的" if status.get('managed', True) else "外部"
        print(f"名称: {status['name']}")
        print(f"IP: {status['ip']}")
        print(f"状态: {status['status']}")
        print(f"PID: {status.get('pid', 'N/A')}")
        print(f"类型: {managed}进程")
        if 'rtmp_server' in status:
            print(f"RTMP服务器: {status['rtmp_server']}")
        print("-" * 40)

if __name__ == "__main__":
    main()
