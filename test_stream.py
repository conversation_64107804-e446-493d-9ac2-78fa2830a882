#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
推流系统测试脚本
用于测试单个摄像头的连接和推流功能
"""

import socket
import subprocess
import time
import argparse
from stable_stream_manager import CameraConfig, StreamProcess
import logging

def test_network_connectivity(ip: str, port: int = 554, timeout: int = 5) -> bool:
    """测试网络连通性"""
    try:
        sock = socket.create_connection((ip, port), timeout=timeout)
        sock.close()
        print(f"✓ 网络连接正常: {ip}:{port}")
        return True
    except (socket.error, socket.timeout) as e:
        print(f"✗ 网络连接失败: {ip}:{port} - {e}")
        return False

def test_rtsp_stream(rtsp_url: str, timeout: int = 10) -> bool:
    """测试RTSP流是否可用"""
    print(f"测试RTSP流: {rtsp_url}")
    
    # 使用ffprobe测试RTSP流
    cmd = [
        "ffprobe",
        "-v", "quiet",
        "-rtsp_transport", "tcp",
        "-i", rtsp_url,
        "-show_entries", "stream=codec_type",
        "-of", "csv=p=0"
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=timeout)
        if result.returncode == 0 and "video" in result.stdout:
            print(f"✓ RTSP流测试成功")
            return True
        else:
            print(f"✗ RTSP流测试失败: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print(f"✗ RTSP流测试超时")
        return False
    except FileNotFoundError:
        print(f"✗ 未找到ffprobe，请确保FFmpeg已安装")
        return False
    except Exception as e:
        print(f"✗ RTSP流测试异常: {e}")
        return False

def test_rtmp_server(rtmp_url: str) -> bool:
    """测试RTMP服务器连通性"""
    # 从RTMP URL中提取服务器地址和端口
    try:
        # rtmp://************:8053/stream/key
        parts = rtmp_url.replace("rtmp://", "").split("/")
        server_part = parts[0]
        
        if ":" in server_part:
            server, port = server_part.split(":")
            port = int(port)
        else:
            server = server_part
            port = 1935  # RTMP默认端口
        
        return test_network_connectivity(server, port)
    except Exception as e:
        print(f"✗ RTMP服务器地址解析失败: {e}")
        return False

def test_single_camera(ip: str, stream_id: str, duration: int = 30):
    """测试单个摄像头的完整推流流程"""
    print(f"\n{'='*60}")
    print(f"测试摄像头: {ip} (Stream ID: {stream_id})")
    print(f"{'='*60}")
    
    # 构建URL
    rtsp_url = f"rtsp://admin:aa987321@{ip}:554/h264/ch1/sub/av_stream"
    rtmp_url = f"rtmp://************:8053/{stream_id}/001"
    
    # 1. 测试网络连通性
    print("\n1. 测试网络连通性...")
    if not test_network_connectivity(ip):
        print("❌ 网络连接失败，跳过后续测试")
        return False
    
    # 2. 测试RTSP流
    print("\n2. 测试RTSP流...")
    if not test_rtsp_stream(rtsp_url):
        print("❌ RTSP流测试失败，跳过推流测试")
        return False
    
    # 3. 测试RTMP服务器
    print("\n3. 测试RTMP服务器...")
    if not test_rtmp_server(rtmp_url):
        print("❌ RTMP服务器连接失败，跳过推流测试")
        return False
    
    # 4. 测试推流
    print(f"\n4. 测试推流 ({duration}秒)...")
    
    # 设置日志
    logger = logging.getLogger("TestStream")
    logger.setLevel(logging.INFO)
    handler = logging.StreamHandler()
    handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
    logger.addHandler(handler)
    
    # 创建摄像头配置
    camera_config = CameraConfig(
        ip=ip,
        rtsp_url=rtsp_url,
        rtmp_url=rtmp_url,
        name=f"Test_Camera_{stream_id}",
        max_retries=2,
        retry_delay=5
    )
    
    # 创建推流进程
    stream = StreamProcess(camera_config, logger)
    
    try:
        # 启动推流
        if stream.start():
            print("✓ 推流启动成功")
            
            # 等待指定时间
            print(f"推流测试中，等待 {duration} 秒...")
            for i in range(duration):
                time.sleep(1)
                status = stream.get_status()
                if status['status'] != 'running':
                    print(f"✗ 推流状态异常: {status['status']}")
                    break
                if i % 10 == 9:  # 每10秒显示一次进度
                    print(f"  进度: {i+1}/{duration} 秒")
            
            # 检查最终状态
            final_status = stream.get_status()
            if final_status['status'] == 'running':
                print("✓ 推流测试成功完成")
                success = True
            else:
                print(f"✗ 推流测试失败: {final_status['status']}")
                success = False
        else:
            print("✗ 推流启动失败")
            success = False
    
    finally:
        # 停止推流
        stream.stop()
        print("推流已停止")
    
    return success

def main():
    parser = argparse.ArgumentParser(description="推流系统测试工具")
    parser.add_argument("--ip", required=True, help="摄像头IP地址")
    parser.add_argument("--stream-id", required=True, help="推流ID")
    parser.add_argument("--duration", type=int, default=30, help="推流测试时长（秒）")
    parser.add_argument("--network-only", action="store_true", help="仅测试网络连通性")
    
    args = parser.parse_args()
    
    if args.network_only:
        # 仅测试网络
        print("测试网络连通性...")
        rtsp_success = test_network_connectivity(args.ip, 554)
        rtmp_success = test_network_connectivity("************", 8053)
        
        if rtsp_success and rtmp_success:
            print("\n✓ 网络连通性测试通过")
        else:
            print("\n✗ 网络连通性测试失败")
    else:
        # 完整测试
        success = test_single_camera(args.ip, args.stream_id, args.duration)
        
        print(f"\n{'='*60}")
        if success:
            print("🎉 测试完成：推流功能正常")
        else:
            print("❌ 测试完成：推流功能异常")
        print(f"{'='*60}")

if __name__ == "__main__":
    main()
