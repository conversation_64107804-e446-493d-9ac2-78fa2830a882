// pages/visitors/visitors.js
let dateTimePicker = require('../../utils/dateTimePicker.js');
let http = require('../../utils/https.js');
Page({
    data: {
        pages: 'vt_yuyue',
        msgNum: 0,
        ucode: '',
        personalData: {},
        //============================
        //是否显示字段
        options: {},
        // 搜索相关
        searchText: '',
        searchResults: [],
        showSearchResults: false,
        showClearIcon: false,
        selectedVisitor: {},
        visitors: [],

        // 被访人搜索相关
        contactSearchText: '',
        contactSearchResults: [],
        showContactSearchResults: false,
        showContactClearIcon: false,
        selectedContact: {},
        emps: [],
        emp_index: 0,

        // 表单数据
        formData: {
            begin_time: '',
            end_time: '',
            reason: '',
            num: 0,
            foods: [],
            remark: '',
            // 访客信息
            visitor_id: null,
            visitor_name: '',
            phone: '',
            post: '',
            sex: '',
            // 被访人信息
            emp_no: '',
            empname: '',
            deptname: '',
            emp_post: '',
            // 访客类型和区域
            vistor_type: '',
            region: ''
        },

        // 时间范围
        dateTime1: null,
        dateTimeArray1: null,
        dateTime2: null,
        dateTimeArray2: null,

        foods: ['午餐', '晚餐'],

        // 访客类型和区域选择
        visitorTypes: [],
        vistor_type_index: -1,
        quyuoptions: [],
        region_index: -1,

        // 控制显示/隐藏的变量
        show_vistype: false,  // 是否显示访客类型
        show_region: false,   // 是否显示区域选择

        // 防抖定时器
        searchTimer: null,
        contactSearchTimer: null
    },
    /**======================================== */
    goPages(e) {
        let thisPage = this.data.pages;
        let toPage = e.currentTarget.dataset.pages;
        if (thisPage != toPage) { //如果不想等 则跳转页面
            wx.reLaunch({
                url: `../${toPage}/${toPage}`,
            })
        }
    },
    getMsgNum(empno) {
        let _this = this;
        http.GET({ //获取未读取消息
            url: 'getMyRemindMessage',
            params: {
                empno: empno,
                ucode: _this.data.ucode
            },
            success: function (res) {
                console.log(res);
                _this.setData({
                    msgNum: res.data.todonum
                })
            },
            fail: function () {}
        });
    },
    login() {
        let _this = this;
        wx.getStorage({
            key: 'data',
            success: function (res) {
                console.log('login', res)
                let data = JSON.parse(res.data);

                _this.setData({
                    personalData: data,
                    date: data.date
                });
                _this.getMsgNum(data.empno)
                _this.initPage();
            },
            fail: function () {
            }
        })
    },
    onLoad() {
        let _this = this;
        wx.getStorage({
            key: 'ucode',
            success: function (res) {
                _this.setData({
                    ucode: res.data
                });
                _this.login();
            },
            fail: function () {
                console.log('-----ucode-fail');
            }
        });
    },

    onShareAppMessage: function (res) {
        if (res.from === 'button') {
            console.log("来自页面内转发按钮");
            console.log(res.target);
        } else {
            console.log("来自右上角转发菜单")
        }
        return {
            title: '宏道科技',
            path: '/pages/index/index',
            imageUrl: "/images/12.png",
            success: (res) => {
                console.log("转发成功", res);
            },
            fail: (res) => {
                console.log("转发失败", res);
            }
        }
    },
    /**======================================== */
    initPage() {
        // 加载显示控制参数和数据
        Promise.all([
            this.loadShowOptions(),
            this.loadVisitorTypes(),
            this.loadRegionOptions()
        ])
            .then(() => {
                this.loadOptions();
            })
            .catch(err => {
                console.error('Failed to load initial data', err);
                wx.showToast({
                    title: '初始化失败',
                    icon: 'none'
                });
            });

        // 获取完整的年月日 时分秒，以及默认显示的数组
        var obj1 = dateTimePicker.dateTimePicker(2025, 2050);
        // 精确到分的处理，将数组的秒去掉
        obj1.dateTimeArray.pop();
        obj1.dateTime.pop();
        var obj2 = dateTimePicker.dateTimePicker(2025, 2050);
        // 精确到分的处理，将数组的秒去掉
        obj2.dateTimeArray.pop();
        obj2.dateTime.pop();

        this.setData({
            dateTimeArray1: obj1.dateTimeArray,
            dateTime1: obj1.dateTime,
            dateTimeArray2: obj2.dateTimeArray,
            dateTime2: obj2.dateTime,
        });
    },
    changeDateTime1(e) {
        let field = e.currentTarget.dataset.field;
        var arr = this.data.dateTime1,
            dateArr = this.data.dateTimeArray1;
        arr[e.detail.column] = e.detail.value;
        dateArr[2] = dateTimePicker.getMonthDay(dateArr[0][arr[0]], dateArr[1][arr[1]]);

        this.setData({
            dateTimeArray1: dateArr,
            dateTime1: arr,
            ['formData.' + field]: dateArr[0][arr[0]] + '-' + dateArr[1][arr[1]] + '-' + dateArr[2][arr[2]] + ' ' + dateArr[3][arr[3]] + ':' + dateArr[4][arr[4]] + ':00',
        });
    },
    changeDateTime2(e) {
        let field = e.currentTarget.dataset.field;
        var arr = this.data.dateTime2,
            dateArr = this.data.dateTimeArray2;
        arr[e.detail.column] = e.detail.value;
        dateArr[2] = dateTimePicker.getMonthDay(dateArr[0][arr[0]], dateArr[1][arr[1]]);

        this.setData({
            dateTimeArray2: dateArr,
            dateTime2: arr,
            ['formData.' + field]: dateArr[0][arr[0]] + '-' + dateArr[1][arr[1]] + '-' + dateArr[2][arr[2]] + ' ' + dateArr[3][arr[3]] + ':' + dateArr[4][arr[4]] + ':00',
        });
    },
    // 加载显示控制参数
    loadShowOptions() {
        return new Promise((resolve, reject) => {
            Promise.all([
                // 加载是否显示访客类型
                new Promise((res, rej) => {
                    http.GET({
                        url: 'all',
                        params: {
                            ucode: this.data.ucode,
                            csCode: 'Vis_show_vistype',
                            api_zpj: 'getOptionsByCode'
                        },
                        success: (response) => {
                            const show_vistype = response.data == '1';
                            this.setData({ show_vistype });
                            console.log('show_vistype:', show_vistype);
                            res(show_vistype);
                        },
                        fail: rej
                    });
                }),
                // 加载是否显示区域选择
                new Promise((res, rej) => {
                    http.GET({
                        url: 'all',
                        params: {
                            ucode: this.data.ucode,
                            csCode: 'Vis_show_region',
                            api_zpj: 'getOptionsByCode'
                        },
                        success: (response) => {
                            const show_region = response.data == '1';
                            this.setData({ show_region });
                            console.log('show_region:', show_region);
                            res(show_region);
                        },
                        fail: rej
                    });
                })
            ]).then(resolve).catch(reject);
        });
    },

    // 加载访客类型
    loadVisitorTypes() {
        return new Promise((resolve, reject) => {
            http.GET({
                url: 'all',
                params: {
                    ucode: this.data.ucode,
                    api_zpj: '/api/vistor/getvistorytype',
                },
                success: (res) => {
                    console.log('getvistorytype', res);
                    this.setData({
                        visitorTypes: res.data
                    });
                    resolve(res.data);
                },
                fail: (err) => {
                    console.log('Failed to load visitor types');
                    wx.showToast({
                        title: '加载访客类型失败',
                        icon: 'none'
                    });
                    reject(err);
                }
            });
        });
    },

    // 加载区域选项
    loadRegionOptions() {
        return new Promise((resolve, reject) => {
            http.GET({
                url: 'all',
                params: {
                    ucode: this.data.ucode,
                    csCode: 'Vis_region',
                    api_zpj: 'getOptionsByCode'
                },
                success: (res) => {
                    console.log('getOptionsByCode', res);
                    var vis_yuyue = res.data.split(',');
                    this.setData({
                        quyuoptions: vis_yuyue
                    });
                    resolve(vis_yuyue);
                },
                fail: (err) => {
                    console.log('Failed to load region options');
                    wx.showToast({
                        title: '加载区域选项失败',
                        icon: 'none'
                    });
                    reject(err);
                }
            });
        });
    },

    loadOptions() {
        http.GET({
            url: 'all',
            params: {
                empno: this.data.personalData.empno,
                ucode: this.data.ucode,
                csCode: 'Vis_yuyue',
                api_zpj: 'getOptionsByCode'
            },
            success: (res) => {
                var vis_yuyue = res.data.split(',');
                var options = {};
                vis_yuyue.forEach(x => options[x] = true);

                console.log('getOptionsByCode', options);

                this.setData({
                    options: options
                });
            }
        });
    },
    // 访客搜索输入事件
    onSearchInput: function (e) {
        const searchText = e.detail.value.trim();
        this.setData({
            searchText: searchText,
            showClearIcon: searchText.length > 0
        });

        // 防抖处理，300ms后执行搜索
        if (this.data.searchTimer) {
            clearTimeout(this.data.searchTimer);
        }

        if (searchText.length >= 1) { // 至少输入2个字符才进行搜索
            this.data.searchTimer = setTimeout(() => {
                this.searchVisitors(searchText);
            }, 300);
        } else {
            this.setData({
                searchResults: [],
                showSearchResults: searchText.length > 0
            });
        }
    },
    onSearchInputFocus: function () {
        if (this.data.searchText.length > 0) {
            this.searchVisitors(this.data.searchText);
            this.setData({
                showSearchResults: true
            });
        }
    },
    onSearchInputBlur: function () {
        this.setData({
            showSearchResults: false
        });
    },
    // 清除访客搜索文本
    clearSearchText: function () {
        this.setData({
            searchText: '',
            searchResults: [],
            showSearchResults: false,
            showClearIcon: false,
            selectedVisitor: null,
            'formData.visitor_id': null,
            'formData.visitor_name': null,
            'formData.phone': null,
            'formData.post': null,
            'formData.sex': null,
        });
    },
    // 搜索访客
    searchVisitors: function (keyword) {
        http.GET({
            url: 'all',
            params: {
                ucode: this.data.ucode,
                api_zpj: '/api/vistor/getvisitor',
                key: keyword
            },
            success: (res) => {
                this.setData({
                    searchResults: res.data.datas,
                    showSearchResults: true
                });
            }
        });
    },
    // 选择访客
    selectVisitor: function (e) {
        const visitorId = e.currentTarget.dataset.id;
        const selectedVisitor = this.data.searchResults.find(item => item.id === visitorId);

        if (selectedVisitor) {
            this.setData({
                selectedVisitor: selectedVisitor,
                searchText: selectedVisitor.name,
                showSearchResults: false,
                showClearIcon: true,
                'formData.visitor_id': selectedVisitor.id,
                'formData.visitor_name': selectedVisitor.name,
                'formData.phone': selectedVisitor.tel,
                'formData.post': selectedVisitor.post,
                'formData.sex': selectedVisitor.sex,
            });
        }
    },
    // 被访人搜索输入事件
    onContactSearchInput: function (e) {
        const searchText = e.detail.value.trim();
        this.setData({
            contactSearchText: searchText,
            showContactClearIcon: searchText.length > 0
        });

        // 防抖处理，300ms后执行搜索
        if (this.data.contactSearchTimer) {
            clearTimeout(this.data.contactSearchTimer);
        }

        if (searchText.length >= 1) { // 被访人搜索至少输入1个字符
            this.data.contactSearchTimer = setTimeout(() => {
                this.searchContacts(searchText);
            }, 300);
        } else {
            this.setData({
                contactSearchResults: [],
                showContactSearchResults: searchText.length > 0
            });
        }
    },
    // 显示被访人搜索结果
    showContactSearchResult: function () {
        if (this.data.contactSearchText.length > 0) {
            this.searchContacts(this.data.contactSearchText);
        }
        this.setData({
            showContactSearchResults: true
        });
    },
    // 清除被访人搜索文本
    clearContactSearchText: function () {
        this.setData({
            contactSearchText: '',
            contactSearchResults: [],
            showContactSearchResults: false,
            showContactClearIcon: false,
            selectedContact: null,
            'formData.emp_no': null,
            'formData.empname': null,
            'formData.deptname': null,
            'formData.post': null
        });
    },
    // 搜索被访人 - 使用远程API
    searchContacts: function (keyword) {
        http.GET({
            url: 'all',
            params: {
                ucode: this.data.ucode,
                api_zpj: '/api/vistor/getemps',
                key

: keyword
            },
            success: (res) => {
                this.setData({
                    contactSearchResults: res.data,
                    showContactSearchResults: true
                });
            }
        });
    },
    // 选择被访人
    selectContact: function (e) {
        const emp_no = e.currentTarget.dataset.id;
        const selectedContact = this.data.contactSearchResults.find(item => item.value === emp_no);

        if (selectedContact) {
            this.setData({
                selectedContact: selectedContact,
                contactSearchText: selectedContact.label,
                showContactSearchResults: false,
                showContactClearIcon: true,
                'formData.emp_no': selectedContact.empno,
                'formData.empname': selectedContact.empname,
                'formData.deptname': selectedContact.deptname,
                'formData.post': selectedContact.duty
            });
        }
    },
    // 访客类型选择
    bindVisitorTypeChange: function (e) {
        this.setData({
            vistor_type_index: e.detail.value,
            'formData.vistor_type': this.data.visitorTypes[e.detail.value].value
        });
    },

    // 区域选择
    bindRegionChange: function (e) {
        this.setData({
            region_index: e.detail.value,
            'formData.region': this.data.quyuoptions[e.detail.value]
        });
    },

    // 通用输入处理函数
    onInput: function (e) {
        const field = e.currentTarget.dataset.field;
        this.setData({
            [`formData.${field}`]: e.detail.value
        });
    },
    bindfoodChange: function (e) {
        const field = e.currentTarget.dataset.field;
        const values = e.detail.value;

        this.setData({
            [`formData.${field}`]: values
        });
    },
    btnCancel(e) {
        wx.navigateBack();
    },
    btnVisitor(e) {
        wx.navigateTo({
            url: '../vt_visitor_add/vt_visitor_add',
        });
    },
    // 提交申请
    submitBtn: function () {
        const formData = this.data.formData;

        // 表单验证
        if (!formData.visitor_id) {
            wx.showToast({
                title: '请选择访客',
                icon: 'none'
            });
            return;
        }

        // 只有在显示访客类型时才验证
        if (this.data.show_vistype && !formData.vistor_type) {
            wx.showToast({
                title: '请选择访客类型',
                icon: 'none'
            });
            return;
        }

        // 只有在显示区域选择时才验证
        if (this.data.show_region && !formData.region) {
            wx.showToast({
                title: '请选择区域',
                icon: 'none'
            });
            return;
        }

        if (!formData.begin_time) {
            wx.showToast({
                title: '请选择起始时间',
                icon: 'none'
            });
            return;
        }

        if (!formData.end_time) {
            wx.showToast({
                title: '请选择结束时间',
                icon: 'none'
            });
            return;
        }

        if (new Date(formData.end_time) <= new Date(formData.begin_time)) {
            wx.showToast({
                title: '结束时间必须晚于起始时间',
                icon: 'none'
            });
            return;
        }

        if (!formData.reason) {
            wx.showToast({
                title: '请输入拜访事由',
                icon: 'none'
            });
            return;
        }

        if (!formData.emp_no) {
            wx.showToast({
                title: '请选择被访人',
                icon: 'none'
            });
            return;
        }

        // 显示加载提示
        wx.showLoading({
            title: '提交中...',
            mask: true
        });

        // 构建提交数据，根据显示控制参数决定是否包含访客类型和区域
        const submitData = {
            ucode: this.data.ucode,
            api_zpj: '/api/vistor/yuyue',
            created_by: this.data.personalData.empno,
            ...formData
        };

        // 只有在显示访客类型时才提交该字段
        if (this.data.show_vistype) {
            submitData.vistor_type = formData.vistor_type;
        } else {
            delete submitData.vistor_type;
        }

        // 只有在显示区域选择时才提交该字段
        if (this.data.show_region) {
            submitData.region = formData.region;
        } else {
            delete submitData.region;
        }

        console.log('提交数据:', submitData);

        http.POST({
            url: 'all',
            params: submitData,
            success: (res) => {
                wx.hideLoading();

                wx.showToast({
                    title: res.data.msg,
                    icon: res.data.success ? 'success' : 'info',
                    duration: 2000
                });

                if (res.data.success) {
                    wx.showModal({
                        title: '继续申请？',
                        content: res.data.msg,
                        showCancel: true,
                        success: function (res) {
                            if (res.confirm) {
                                wx.reLaunch({
                                    url: '../vt_yuyue_add/vt_yuyue_add',
                                });
                            } else {
                                wx.reLaunch({
                                    url: '../home/<USER>',
                                });
                            }
                        }
                    })
                }
            }
        });
    }
})