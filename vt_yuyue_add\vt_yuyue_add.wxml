<!--pages/visitors/visitors.wxml-->
<view class="page_container">

<!-- 表单内容 -->
<view class="form-container">
  <view class="split"></view>
    <!-- 已选人员信息 -->
    <view>
        <!-- 搜索框 -->
        <view class="search-box">
            <view class="search-input-container">
                <icon type="search"></icon>
                <input bindinput="onSearchInput" bindfocus="onSearchInputFocus" bindblur="onSearchInputBlur" placeholder="搜索注册访客" value="{{searchText}}" />
            </view>
            <view wx:if="{{showClearIcon}}" class="clear-icon" bindtap="clearSearchText">
                <icon type="clear"></icon>
                <scroll-view class="search_result" scroll-y></scroll-view>
            </view>
        </view>

        <!-- 搜索结果列表 -->
        <scroll-view class="search_result" scroll-y>
            <view class="result-list" wx:if="{{showSearchResults && searchResults.length > 0}}">
                <view class="result-item" wx:for="{{searchResults}}" wx:key="id" bindtap="selectVisitor" data-id="{{item.id}}">
                    <view class="item-left">
                        <view class="item-name"> {{item.name}}
                            <text class="item-label">{{item.tel}}</text>
                        </view>
                    </view>
                </view>
            </view>
            <view class="empty-result" wx:if="{{showSearchResults && searchResults.length === 0}}">
                <text>未找到相关人员</text>
            </view>
        </scroll-view>
        <view class="selected-user-info">
            <view class="form-item0" hidden="{{options['访客姓名']}}">
                <view class="form-label">访客姓名：</view>
                <view class="form-value">{{selectedVisitor.name}}</view>
            </view>
            <view class="form-item0" hidden="{{options['访客电话']}}">
                <view class=" form-label ">访客电话：</view>
                <view class=" form-value">{{selectedVisitor.tel}}</view>
            </view>
            <view class="form-item0" hidden="{{options['访客职务']}}">
                <view class="form-label ">访客职务：</view>
                <view class="form-value">{{selectedVisitor.position}}</view>
            </view>
            <view class="form-item0" hidden="{{options['性别']}}">
                <view class="form-label ">性别：</view>
                <view class="form-value"> {{selectedVisitor.sex}}</view>
            </view>
            <view class="form-item0" hidden="{{options['车牌号码']}}">
                <view class="form-label ">车牌号码：</view>
                <view class="form-value">{{selectedVisitor.car_num}}</view>
            </view>
        </view>
    </view>
    <view class="split"> </view>

    <!-- 访客类型选择 -->
    <view class="form-item" wx:if="{{show_vistype}}">
        <view class="form-label required">访客类型：</view>
        <picker bindchange="bindVisitorTypeChange" value="{{vistor_type_index}}" range="{{visitorTypes}}" range-key="label">
            <view class="form-value">
                {{visitorTypes[vistor_type_index].label || '请选择访客类型'}}
            </view>
        </picker>
    </view>

    <!-- 区域选择 -->
    <view class="form-item" wx:if="{{show_region}}">
        <view class="form-label required">区域：</view>
        <picker bindchange="bindRegionChange" value="{{region_index}}" range="{{quyuoptions}}">
            <view class="form-value">
                {{quyuoptions[region_index] || '请选择区域'}}
            </view>
        </picker>
    </view>

    <!-- 时间选择 -->
    <view class="form-item">
        <view class="form-label required">预约时间：</view>
        <view class="form-value">
            <picker mode="multiSelector" value="{{dateTime1}}" bindchange="changeDateTime1" bindcolumnchange="changeDateTime1" range="{{dateTimeArray1}}" data-field="begin_time">
                <view class="tui-picker-detail">
                    <text>{{formData.begin_time||'开始时间'}}</text>
                </view>
            </picker>
            <picker mode="multiSelector" value="{{dateTime2}}" bindchange="changeDateTime2" bindcolumnchange="changeDateTime2" range="{{dateTimeArray2}}" data-field="end_time">
                <view class="tui-picker-detail">
                    <text>{{formData.end_time||'结束时间'}}</text>
                </view>
            </picker>
        </view>
    </view>

    <!-- 拜访事由 -->
    <view class="form-item" hidden="{{options['拜访事由']}}">
        <view class="form-label required">拜访事由：</view>
        <input type="text" placeholder="请输入拜访事由" bindinput="onInput" data-field="reason" value="{{formData.reason}}" />
    </view>

    <!-- 被访人 -->
    <view class="form-item" hidden="{{options['被访人']}}">
        <view class="form-label required">被访人：</view>
        <view class="search-box">
            <view class="search-input-container">
                <icon type="search"></icon>
                <input bindinput="onContactSearchInput" bindfocus="showContactSearchResult" placeholder="搜索被访人" value="{{contactSearchText}}" />
            </view>
            <view wx:if="{{showContactClearIcon}}" class="clear-icon" bindtap="clearContactSearchText">
                <icon type="clear"></icon>
            </view>
        </view>
        <scroll-view class="search_result" scroll-y>
            <view class="result-list" wx:if="{{showContactSearchResults && contactSearchResults.length > 0}}">
                <view class="result-item" wx:for="{{contactSearchResults}}" wx:key="value" bindtap="selectContact" data-id="{{item.value}}">
                    <view class="item-left">
                        <view class="item-name">{{item.label}}</view>
                    </view>
                </view>
            </view>
        </scroll-view>
    </view>

    <!-- 随行人数 -->
    <view class="form-item" hidden="{{options['随行人数']}}">
        <view class="form-label">随行人数</view>
        <input type="number" placeholder="请输入随行人数" bindinput="onInput" data-field="num" value="{{formData.num}}" />
    </view>

    <!-- 是否就餐 -->
    <view class="form-item" hidden="{{options['是否就餐']}}">
        <view class="form-label">是否就餐：</view>
        <checkbox-group bindchange="bindfoodChange" data-field="foods">
            <view class="checkbox-group">
                <label class="checkbox-label" wx:for="{{foods}}" wx:key="value">
                    <checkbox value="{{item}}" />{{item}}
                </label>
            </view>
        </checkbox-group>
    </view>

    <!-- 备注 -->
    <view class="form-item" hidden="{{options['备注']}}">
        <view class="form-label">备注：</view>
        <textarea style="height: 70rpx; " placeholder="请输入备注信息" bindinput="onInput" data-field="remark" value="{{formData.remark}}" />
    </view>
</view>
</view>

<!--底部栏-->
<view class='twoTabBar comTabBar'>
<view class='oneTabBarTouch' bindtap='goPages' data-pages='home'>
    <image src='../../icon/home.png'></image>
    返回主页
</view>
<view class='oneTabBarTouch' bindtap='btnVisitor'>
    <image src='../../icon/home.png'></image>
    注册访客
</view>
<view class='oneTabBarTouch' bindtap='submitBtn'>
    <image src='../../icon/submit.png'></image>
    提交
</view>
</view>
<!--底部栏-->