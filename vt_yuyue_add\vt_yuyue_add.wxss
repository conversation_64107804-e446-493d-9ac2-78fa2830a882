/* pages/visitors/visitors.wxss */
Page {
    background: #fff;
    font-size: 26rpx;
}
.split {
  height: 20rpx;
  background-color: #f0f0f0;
}

.page_container {
    min-height: 100vh;
    padding-bottom: 110rpx;
}

/* 头部样式 */
.header {
    padding: 30rpx 0;
    text-align: center;
}

.header-title {
    font-size: 28rpx;
    color: #333;
}

.header-subtitle {
    font-size: 24rpx;
    color: #666;
    margin-top: 10rpx;
}

/* 搜索框样式 */
.search-container {
    padding: 10rpx 0;
}

.search-box {
    display: flex;
    align-items: center;
    background-color: rgb(255, 255, 255);
    border-radius: 10rpx;
    padding: 15rpx 20rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.search-input-container {
    flex: 1;
    display: flex;
    align-items: center;
}


.search-placeholder {
    color: #999;
    font-size: 24rpx;
}

.search_result {
    border: 1rpx solid #f0f0f0;
    background-color: #e6cbcb;
    max-height: 600rpx;

}

.clear-icon {
    width: 30rpx;
    height: 30rpx;
    margin-left: 15rpx;
}

/* 搜索结果样式 */
.result-container {
    margin-top: 10rpx;
    border-radius: 10rpx;
    background-color: #fff;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    max-height: 600rpx;
    overflow-y: auto;
}

.result-list {
    padding: 0 20rpx;
}

.result-item {
    display: flex;
    align-items: center;
    padding: 20rpx 0;
    border-bottom: 1rpx solid #eee;
}

.result-item:last-child {
    border-bottom: none;
}

.item-left {
    flex: 1;
}

.item-name {
    font-size: 24rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 10rpx;
}

.item-info {
    display: flex;
    flex-wrap: wrap;
}

.item-label {
    font-size: 24rpx;
    color: #666;
    margin-right: 20rpx;
    margin-bottom: 5rpx;
}

.item-right {
    margin-left: 20rpx;
}

.item-avatar {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    background-color: #f0f0f0;
}

.empty-result {
    padding: 80rpx 0;
    text-align: center;
}

.empty-result image {
    width: 160rpx;
    height: 160rpx;
    margin-bottom: 20rpx;
}

.empty-result text {
    font-size: 26rpx;
    color: #999;
}

/* 已选人员样式 */
.selected-user-info {
    margin-top: 25rpx;
}


.user-name {
    font-size: 28rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 10rpx;
}

.user-info {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 10rpx;
}

.user-info text {
    font-size: 24rpx;
    color: #666;
    margin-right: 20rpx;
    margin-bottom: 5rpx;
}

.user-contact {
    display: flex;
    flex-wrap: wrap;
}

.user-contact text {
    font-size: 24rpx;
    color: #999;
    margin-right: 20rpx;
    margin-bottom: 5rpx;
}

.change-btn {
    padding: 10rpx 20rpx;
    border-radius: 5rpx;
    background-color: #f0f0f0;
    color: #666;
    font-size: 24rpx;
}

/* 表单样式 */
.form-container {
   /* margin: 20rpx 20rpx; */

    
}

.form-item0 {
    background-color: #fff;
    padding: 10rpx 40rpx; margin: 20rpx;
    /*border-bottom: 1rpx solid #f0f0f0;*/
    display: flex;
}

.form-item {
    background-color: #fff;
    padding: 20rpx 40rpx;
    margin: 10rpx;
    border-bottom: 1rpx solid #d9d9d9;
}

.tui-picker-detail {
    display: flex;
    justify-content: space-between;
    margin: 20rpx;
}



.form-label {
    width: 140rpx;
    padding-bottom: 10rpx;
    color: #747474;
}

.form-value {
    color: #333;
    flex: 1;
}

/* picker样式 */
.form-value picker {
    width: 100%;
}

.tui-picker-detail {
    padding: 10rpx 0;
    color: #333;
}

.tui-picker-detail text {
    color: #333;
}

/* 必填项标识 */
.required::after {
    content: '*';
    color: red;
    margin-left: 4rpx;
}

.split {
    height: 20rpx;
    background-color: #f0f0f0;
}

.picker {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 24rpx;
    color: #666;
}

.picker image {
    width: 24rpx;
    height: 24rpx;
}

input,
textarea {
    font-size: 24rpx;
    color: #333;
    width: 100%;
}

.input-placeholder {
    color: #999;
    font-size: 24rpx;
}

.textarea-placeholder {
    color: #999;
    font-size: 24rpx;
}


/* 被访人样式 */
.selected-contact-info {
    margin-top: 10rpx;
}

.contact-card {
    background-color: #fff;
    border-radius: 10rpx;
    padding: 25rpx 20rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
}

.contact-avatar {
    margin-right: 20rpx;
}

.contact-avatar image {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    background-color: #f0f0f0;
}

.contact-details {
    flex: 1;
}

.contact-name {
    font-size: 30rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 10rpx;
}

.contact-info {
    display: flex;
    flex-wrap: wrap;
}

.contact-info text {
    font-size: 26rpx;
    color: #666;
    margin-right: 20rpx;
    margin-bottom: 5rpx;
}

/* 提交按钮 */
.submit-container {
    padding: 30rpx 20rpx;
}

.submit-btn {
    width: 100%;
    height: 90rpx;
    line-height: 90rpx;
    background-color: #07c160;
    color: #fff;
    font-size: 32rpx;
    font-weight: bold;
    border-radius: 45rpx;
    box-shadow: 0 4rpx 12rpx rgba(7, 193, 96, 0.2);
}

.checkbox-group {
    display: flex;
    flex-wrap: wrap;
}

.checkbox-label {
    display: flex;
    align-items: center;
    margin-right: 40rpx;
    margin-bottom: 10rpx;
}

.checkbox-label checkbox {
    transform: scale(0.8);
}

.checkbox-label {
    font-size: 24rpx;
    color: #666;
}