let dateTimePicker = require('../../utils/dateTimePicker.js');
let http = require('../../utils/https.js');

Page({
  data: {
    options: {},
    personalData: {},
    ucode: '',
    formData: {
      id: 0,
      foods: [],
      ppes: [],
      begin_time: '',
      end_time: '',
      is_over: false,
      files: [],
      vistor_type: '',
      region: '',
      is_can_region:false
    },
    qrCodeUrl: '',
    foods: [],
    ppes: [],
    dateTime1: null,
    dateTimeArray1: null,
    dateTime2: null,
    dateTimeArray2: null,
    visitorTypes: [],
    vistor_type_index: -1,
    quyuoptions: [],
    region_index: -1
  },

  onLoad(options) {
    this.setData({
      options: options
    });
    wx.getStorage({
      key: 'ucode',
      success: (res) => {
        this.setData({
          ucode: res.data
        });
        this.login();
        this.initPage();
      },
      fail: function () {
        console.log('-----ucode-fail');
      }
    });
  },

  login() {
    wx.getStorage({
      key: 'data',
      success: (res) => {
        let data = JSON.parse(res.data);
        this.setData({
          personalData: data
        });
      },
      fail: function () {}
    });
  },

  goPages(e) {
    let thisPage = this.data.pages;
    let toPage = e.currentTarget.dataset.pages;

    wx.navigateTo({
      url: '../vt_yuyue_list/vt_yuyue_list',
    })
  },

  onShareAppMessage: function (res) {
    if (res.from === 'button') {
      console.log("来自页面内转发按钮");
      console.log(res.target);
    } else {
      console.log("来自右上角转发菜单")
    }
    return {
      title: '宏道科技',
      path: '/pages/index/index',
      imageUrl: "/images/12.png",
      success: (res) => {
        console.log("转发成功", res);
      },
      fail: (res) => {
        console.log("转发失败", res);
      }
    }
  },

  // 通用输入处理函数
  bindChange: function (e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;

    // 防止重复触发
    if (this.changeTimeout) {
      clearTimeout(this.changeTimeout);
    }

    this.changeTimeout = setTimeout(() => {
      console.log(field, value);

      if (field === 'foods') {
        // 特殊处理foods字段，同时更新formData.foods和foods选项的checked状态
        const updatedFoods = this.data.foods.map(item => ({
          ...item,
          checked: value.includes(item.value)
        }));

        this.setData({
          [`formData.${field}`]: value,
          foods: updatedFoods
        });

        console.log('Updated formData.foods:', this.data.formData.foods);
        console.log('Updated foods options:', updatedFoods);
      } else {
        // 其他字段正常处理
        this.setData({
          [`formData.${field}`]: value
        });
      }
    }, 50); // 50ms防抖
  },

  bindVisitorTypeChange: function (e) {
    this.setData({
      vistor_type_index: e.detail.value,
      'formData.vistor_type': this.data.visitorTypes[e.detail.value].value
    });
  },

  bindRegionChange: function (e) {
    this.setData({
      region_index: e.detail.value,
      'formData.region': this.data.quyuoptions[e.detail.value]
    });
  },

  initPage() {
    Promise.all([this.loadVisitorTypes(), this.loadOptions()])
      .then(() => {
        this.gethumbase();
        this.loadDetail();
        this.fetchReservationDetail();
      })
      .catch(err => {
        console.error('Failed to load initial data', err);
        wx.showToast({
          title: '初始化失败',
          icon: 'none'
        });
      });

    // 获取完整的年月日 时分秒，并移除秒数
    var obj1 = dateTimePicker.dateTimePicker(2025, 2050);
    obj1.dateTimeArray.pop(); // 移除秒
    obj1.dateTime.pop();
    var obj2 = dateTimePicker.dateTimePicker(2025, 2050);
    obj2.dateTimeArray.pop(); // 移除秒
    obj2.dateTime.pop();
    this.setData({
      dateTimeArray1: obj1.dateTimeArray,
      dateTime1: obj1.dateTime,
      dateTimeArray2: obj1.dateTimeArray,
      dateTime2: obj1.dateTime
    });
  },

  loadVisitorTypes() {
    return new Promise((resolve, reject) => {
      http.GET({
        url: 'all',
        params: {
          ucode: this.data.ucode,
          api_zpj: '/api/vistor/getvistorytype',
        },
        success: (res) => {
          console.log('getvistorytype', res);
          this.setData({
            visitorTypes: res.data
          });
          resolve();
        },
        fail: (err) => {
          console.log('Failed to load visitor types', err);
          wx.showToast({
            title: '加载访客类型失败',
            icon: 'none'
          });
          reject(err);
        }
      });
    });
  },

  loadOptions() {
    return new Promise((resolve, reject) => {
      http.GET({
        url: 'all',
        params: {
          ucode: this.data.ucode,
          csCode: 'Vis_region',
          api_zpj: 'getOptionsByCode'
        },
        success: (res) => {
          console.log('getOptionsByCode', res);
          var vis_yuyue = res.data.split(',');
          this.setData({
            quyuoptions: vis_yuyue
          });
          resolve();
        },
        fail: (err) => {
          console.log('Failed to load region options', err);
          wx.showToast({
            title: '加载区域选项失败',
            icon: 'none'
          });
          reject(err);
        }
      });
    });
  },

  gethumbase() {
    http.GET({
      url: 'all',
      params: {
        ucode: this.data.ucode,
        api_zpj: '/api/vistor/gethumbase',
        parentno: 'vistor_protect_type'
      },
      success: (res) => {
        this.setData({
          ppes: res.data
        });
      }
    });

    http.GET({
      url: 'all',
      params: {
        ucode: this.data.ucode,
        api_zpj: '/api/vistor/gethumbase',
        parentno: 'vistor_food'
      },
      success: (res) => {
        this.setData({
          foods: res.data
        });
      }
    });
  },

  // changeDateTime1(e) {
  //   let field = e.currentTarget.dataset.field;
  //   var arr = this.data.dateTime1,
  //     dateArr = this.data.dateTimeArray1;
  //   arr[e.detail.column] = e.detail.value;
  //   dateArr[2] = dateTimePicker.getMonthDay(dateArr[0][arr[0]], dateArr[1][arr[1]]);

  //   this.setData({
  //     dateTimeArray1: dateArr,
  //     dateTime1: arr,
  //     ['formData.' + field]: dateArr[0][arr[0]] + '-' + dateArr[1][arr[1]] + '-' + dateArr[2][arr[2]] + ' ' + dateArr[3][arr[3]] + ':' + dateArr[4][arr[4]] + ':00',
  //   });
  // },

  // changeDateTime2(e) {
  //   let field = e.currentTarget.dataset.field;
  //   var arr = this.data.dateTime2,
  //     dateArr = this.data.dateTimeArray2;
  //   arr[e.detail.column] = e.detail.value;
  //   dateArr[2] = dateTimePicker.getMonthDay(dateArr[0][arr[0]], dateArr[1][arr[1]]);

  //   this.setData({
  //     dateTimeArray2: dateArr,
  //     dateTime2: arr,
  //     ['formData.' + field]: dateArr[0][arr[0]] + '-' + dateArr[1][arr[1]] + '-' + dateArr[2][arr[2]] + ' ' + dateArr[3][arr[3]] + ':' + dateArr[4][arr[4]] + ':00',
  //   });
  // },

  changeDateTime1: function(e) {
    this.changeDateTime(e, 'begin_time', 'dateTime1');
  },
  
  changeDateTime2: function(e) {
    // 先检查是否已选择 begin_time
    if (!this.data.formData.begin_time) {
      wx.showToast({
        title: '请先选择来访时间',
        icon: 'none'
      });
      return; // 忽略更新
    }
  
    // 继续正常处理
    this.changeDateTime(e, 'end_time', 'dateTime2');
  },
  
// 处理 bindchange 事件
changeDateTime: function(e, field, dateTimeKey) {
  // 确保是 bindchange 事件（e.detail.value 是数组）
  if (!Array.isArray(e.detail.value)) {
    console.error('Expected array for bindchange, got:', e.detail.value);
    return;
  }

  var arr = e.detail.value;
  var dateArray = this.data[`dateTimeArray${field === 'begin_time' ? 1 : 2}`];

  // 调试：检查输入
  console.log(`changeDateTime: field=${field}, arr=${JSON.stringify(arr)}, dateArray=${JSON.stringify(dateArray)}`);

  // 验证 dateArray 完整性（只到分钟，5 个部分）
  if (!dateArray || !Array.isArray(dateArray) || dateArray.length !== 5 ||
      !dateArray[0] || !dateArray[1] || !dateArray[2] || !dateArray[3] || !dateArray[4]) {
    console.error('Invalid dateTimeArray:', dateArray);
    wx.showToast({
      title: '时间选择器数据异常',
      icon: 'none'
    });
    return;
  }

  // 确保索引有效（只检查 5 个索引：年、月、日、时、分）
  for (let i = 0; i < 5; i++) {
    if (!Number.isInteger(arr[i]) || arr[i] < 0 || arr[i] >= dateArray[i].length) {
      console.error('Invalid picker index:', arr);
      wx.showToast({
        title: '时间选择不完整',
        icon: 'none'
      });
      return;
    }
  }

  let year = dateArray[0][arr[0]];
  let month = dateArray[1][arr[1]];
  let day = dateArray[2][arr[2]];
  let hour = dateArray[3][arr[3]];
  let minute = dateArray[4][arr[4]];

  // 确保所有部分有效
  if (year == null || month == null || day == null || hour == null || minute == null) {
    console.error('Invalid date components:', { year, month, day, hour, minute });
    wx.showToast({
      title: '时间选择不完整',
      icon: 'none'
    });
    return;
  }

  // 格式化时间（补零）
  month = String(month).padStart(2, '0');
  day = String(day).padStart(2, '0');
  hour = String(hour).padStart(2, '0');
  minute = String(minute).padStart(2, '0');
  let value = `${year}-${month}-${day} ${hour}:${minute}`;

  // 调试：检查拼接结果
  console.log(`Formatted ${field}:`, value);

  // 验证时间格式
  let parsedDate = new Date(value.replace(/-/g, '/'));
  if (isNaN(parsedDate.getTime())) {
    console.error('Invalid date format:', value);
    wx.showToast({
      title: '时间格式错误',
      icon: 'none'
    });
    return;
  }

  // 结束时间验证：至少晚于 begin_time 15 分钟
  if (field === 'end_time') {
    let beginDate = new Date(this.data.formData.begin_time.replace(/-/g, '/'));
    let endDate = parsedDate;
    let minEndTime = new Date(beginDate.getTime() + 15 * 60 * 1000);

    if (isNaN(beginDate.getTime())) {
      console.error('Invalid begin_time:', this.data.formData.begin_time);
      wx.showToast({
        title: '来访时间格式错误',
        icon: 'none'
      });
      return;
    }

    if (endDate < minEndTime) {
      // 自动设置为 begin_time + 15 分钟
      let year = minEndTime.getFullYear();
      let month = String(minEndTime.getMonth() + 1).padStart(2, '0');
      let day = String(minEndTime.getDate()).padStart(2, '0');
      let hour = String(minEndTime.getHours()).padStart(2, '0');
      let minute = String(minEndTime.getMinutes()).padStart(2, '0');
      value = `${year}-${month}-${day} ${hour}:${minute}`;

      // 查找 dateTimeArray 中最接近的索引
      let newArr = this.findClosestDateTimeIndex(dateArray, minEndTime);

      wx.showToast({
        title: '结束时间已调整为来访时间 + 15分钟',
        icon: 'none'
      });

      this.setData({
        [`formData.${field}`]: value,
        [dateTimeKey]: newArr
      });
      return;
    }
  }

  // 正常更新
  this.setData({
    [`formData.${field}`]: value,
    [dateTimeKey]: arr
  });
},
// 处理 bindcolumnchange 事件，动态调整日期范围（如 2 月天数）
columnChange: function(e, field, dateTimeKey) {
  // e.detail = { column, value }
  const { column, value } = e.detail;
  console.log(`columnChange: field=${field}, column=${column}, value=${value}`);

  // 获取当前 picker 的值和数组
  let dateTime = this.data[dateTimeKey];
  let dateArray = this.data[`dateTimeArray${field === 'begin_time' ? 1 : 2}`];

  // 更新当前列的索引
  dateTime[column] = value;

  // 动态调整日期范围（例如，2 月只有 28/29 天）
  let newDateArray = JSON.parse(JSON.stringify(dateArray)); // 深拷贝
  if (column === 0 || column === 1) { // 年或月变化，更新日数组
    let year = newDateArray[0][dateTime[0]];
    let month = newDateArray[1][dateTime[1]];
    let daysInMonth = new Date(year, month, 0).getDate();
    newDateArray[2] = Array.from({ length: daysInMonth }, (_, i) => String(i + 1).padStart(2, '0'));
    // 调整日索引
    if (dateTime[2] >= daysInMonth) {
      dateTime[2] = daysInMonth - 1;
    }
  }

  this.setData({
    [`dateTimeArray${field === 'begin_time' ? 1 : 2}`]: newDateArray,
    [dateTimeKey]: dateTime
  });
},
// 辅助函数：查找 dateTimeArray 中最接近目标时间的索引
findClosestDateTimeIndex(dateArray, targetDate) {
  let year = targetDate.getFullYear();
  let month = targetDate.getMonth() + 1;
  let day = targetDate.getDate();
  let hour = targetDate.getHours();
  let minute = targetDate.getMinutes();

  // 查找最接近的索引
  let yearIdx = dateArray[0].indexOf(year);
  let monthIdx = dateArray[1].indexOf(String(month).padStart(2, '0'));
  let dayIdx = dateArray[2].indexOf(String(day).padStart(2, '0'));
  let hourIdx = dateArray[3].indexOf(String(hour).padStart(2, '0'));
  let minuteIdx = dateArray[4].indexOf(String(minute).padStart(2, '0'));

  // 如果索引无效，选择最接近的可用值
  if (yearIdx === -1) yearIdx = dateArray[0].length - 1;
  if (monthIdx === -1) monthIdx = dateArray[1].indexOf('01');
  if (dayIdx === -1) dayIdx = dateArray[2].indexOf('01');
  if (hourIdx === -1) hourIdx = dateArray[3].indexOf('00');
  if (minuteIdx === -1) minuteIdx = dateArray[4].indexOf('00');

  return [yearIdx, monthIdx, dayIdx, hourIdx, minuteIdx];
},
  fetchReservationDetail() {
    wx.showLoading({
      title: '加载中...',
    });

    console.log(this.fetchReservationDetail.name, this.data)
    http.GET({
      url: 'all',
      params: {
        ucode: this.data.ucode,
        api_zpj: '/api/vistor/getapprovalinfo',
        created_by: this.data.personalData.empno,
        id: this.data.options.id
      },
      success: (res) => {
        var formData = res.data.data;
        var foods = formData.foods || []; // 确保foods是数组
        var ppes = formData.ppes || []; // 确保ppes是数组
        var files = formData.files || []; // 确保files是数组
        var fcode = formData.fcode;

        console.log('接口返回的foods:', foods);
        console.log('接口返回的formData:', formData);
        fcode = formData.qrCodeUrl ? fcode : '';
        if(formData.fstate>=2){
          wx.setNavigationBarTitle({
            title: '审批明细',
          })
        }
        if (!res.data.data.files)
          res.data.data.files = [];

        var qrCodeUrl = http.API_URL + 'qrcode?text=' + fcode;

        console.log('getapprovalinfo', formData, this.data);

        let visitorTypeIndex = 0;
        let regionIndex = 0;
        if (this.data.visitorTypes.length > 0 && formData.vistor_type) {
          visitorTypeIndex = this.data.visitorTypes.findIndex(type => type.value === formData.vistor_type);
          console.log(999,visitorTypeIndex)
          // if (visitorTypeIndex === -1) visitorTypeIndex = null;
        }else{
          visitorTypeIndex=-1;
        }
        if (this.data.quyuoptions.length > 0 && formData.region) {
          regionIndex = this.data.quyuoptions.findIndex(region => region === formData.region);
          // if (regionIndex === -1) regionIndex = 0;
        }
        else{
          regionIndex=-1;
        }

        // 更新foods选项的选中状态
        const updatedFoods = this.data.foods.map(x => ({
          ...x,
          checked: foods ? foods.includes(x.value) : false
        }));

        // 更新ppes选项的选中状态
        const updatedPpes = this.data.ppes.map(x => ({
          ...x,
          checked: ppes ? ppes.includes(x.value) : false
        }));

        this.setData({
          formData: {
            ...this.data.formData,
            ...formData,
            vistor_type: formData.vistor_type || '',
            region: formData.region || '',
            foods: foods // 直接存储foods数组，已经确保是数组了
          },
          qrCodeUrl: qrCodeUrl,
          foods: updatedFoods,
          ppes: updatedPpes,
          vistor_type_index: visitorTypeIndex,
          region_index: regionIndex
        });

        // 延迟一下再打印，确保setData完成
        setTimeout(() => {
          console.log('设置完成后的formData.foods:', this.data.formData.foods);
          console.log('设置完成后的foods选项:', this.data.foods);
        }, 100);
        console.log(666,visitorTypeIndex,this.data.vistor_type_index)
      },
      complete: () => {
        wx.hideLoading();
      }
    });
    wx.hideLoading();
  },

  loadDetail() {
  },

  uploadImage() {
    wx.chooseImage({
      count: 5 - this.data.formData.files.length,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const filePaths = res.tempFilePaths;
        const uploadTasks = filePaths.map(path => {
          return new Promise((resolve, reject) => {
            wx.uploadFile({
              url: http.ROOT_URL + '/api/vistor/upload',
              filePath: path,
              name: 'file',
              formData: {
                ucode: this.data.ucode
              },
              success: (uploadRes) => {
                let data = JSON.parse(uploadRes.data);
                if (data.success) {
                  resolve(data.data);
                } else {
                  reject(data.msg);
                }
              },
              fail: reject
            });
          });
        });

        Promise.all(uploadTasks).then(urls => {
          this.setData({
            'formData.files': [...this.data.formData.files, ...urls]
          });
        }).catch(err => {
          wx.showToast({
            title: '上传失败',
            icon: 'none'
          });
        });
      }
    });
  },

  // 删除图片
  deleteImage(e) {
    const index = e.currentTarget.dataset.index;
    let files = this.data.formData.files;

    if (this.data.formData.fstate == '3' && files.length <= 2) {
      wx.showToast({
        title: '不能删除',
      })
      return;
    }

    if (this.data.formData.fstate == '2' && files.length <= 1) {
      wx.showToast({
        title: '不能删除',
      })
      return;
    }

    wx.showModal({
      title: '确认删除',
      content: '确定要删除这张图片吗？',
      success: (res) => {
        if (res.confirm) {
          files.splice(index, 1);
          this.setData({
            ['formData.files']: files
          });
        }
      }
    });
  },

  // 预览图片
  previewImage(e) {
    var current = e.currentTarget.dataset.url;
    var files = this.data.formData.files;
    if (e.currentTarget.dataset.type == 'qrcode') {
      files = [current]
    }

    wx.previewImage({
      current,
      urls: files
    });
  },

  // 提交表单
  submitForm() {
    this.yuyue_edit_app({
      success: function () {
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    });
  },

  yuyue_edit_app(option) {
    var formData = this.data.formData;
    console.log(999,this.data.vistor_type_index)
    if(this.data.vistor_type_index<0){
      wx.showToast({
        title: '请选择访客类型',
        icon: 'none'
      });
      return;
    }
    if(this.data.region_index<0){
      wx.showToast({
        title: '请选择区域',
        icon: 'none'
      });
      return;
    }

    if (!formData.begin_time) {
      wx.showToast({
        title: '请选择起始时间',
        icon: 'none'
      });
      return;
    }

    if (!formData.end_time) {
      wx.showToast({
        title: '请选择结束时间',
        icon: 'none'
      });
      return;
    }

    if (new Date(formData.end_time) <= new Date(formData.begin_time)) {
      wx.showToast({
        title: '结束时间必须晚于起始时间',
        icon: 'none'
      });
      return;
    }

    // 提货送货，1审核必须图片
    if (this.data.formData.vistor_type == '提货送货' &&
      this.data.formData.fstate == '2') {
      var files = this.data.formData.files;
      if (!files || files.length == 0) {
        wx.showToast({
          title: '请上传图片',
          icon: 'none'
        });
        return;
      }
    }

    // 提货送货，结束，必须有2张图片
    if (this.data.formData.vistor_type == '提货送货' &&
      this.data.formData.is_over) {
      var files = this.data.formData.files;
      if (!files || files.length < 2) {
        wx.showToast({
          title: '请上传图片',
          icon: 'none'
        });
        return;
      }
    }

    // 显示加载提示
    wx.showLoading({
      title: '提交中...',
    });

    http.POST({
      url: 'all',
      params: {
        ucode: this.data.ucode,
        api_zpj: '/api/vistor/yuyue_edit_app',
        created_by: this.data.personalData.empno,
        ...formData
      },
      success: (res) => {
        wx.showToast({
          title: res.data.msg,
          icon: 'success'
        });
        if (res.data.success) {
          if (option.success)
            option.success();
        }
      },
      fail: (err) => {},
      complete: () => {
        wx.hideLoading();
      }
    });
  },

  canapproval(option) {
    http.GET({
      url: 'canapproval',
      params: {
        tabType: '访客',
        allselectid: this.data.formData.id,
        kind: option.kind,
        Checker: this.data.personalData.empname,
        ucode: this.data.ucode,
        visitorType: this.data.formData.vistor_type,
        region: this.data.formData.region
      },
      success: (res) => {
        console.log(res)
        wx.showModal({
          title: '提示',
          content: res.data.msg,
          showCancel: false,
          success: function (res) {
            if (res.confirm) {
              const pages = getCurrentPages()
              const prevPage = pages[pages.length - 2]
              console.log('canapproval', prevPage)
              prevPage.doReload()
              wx.navigateBack()
            }
          }
        })
      },
      fail: function () {},
    });
  },

  goUpload(e) {
    let kind = e.currentTarget.dataset.type;

    if (kind == 0) {
      this.yuyue_edit_app({
        success: () => {
          this.canapproval({
            kind: kind
          });
        }
      });
    } else {
      this.canapproval({
        kind: kind
      });
    }
  },

  // 发起区域变更
  initiateRegionChange() {
    const { formData } = this.data;
    if (!formData.region) {
      wx.showToast({
        title: '请选择区域',
        icon: 'none'
      });
      return;
    }
  
    wx.showLoading({
      title: '提交变更...',
    });
  
    http.POST({
      url: 'all',
      params: {
        ucode: this.data.ucode,
        api_zpj: '/api/vistor/changeRegion',
        id: formData.id,
        region: formData.region
      },
      success: (res) => {
        console.log('API Response:', res); // 调试：打印完整响应
        if (res && res.data) {
          const { status, message } = res.data;
          
          wx.hideLoading(); // 成功后隐藏加载提示
          wx.showModal({
            title: status === 'success' ? '成功' : '失败',
            content: message,
            showCancel: false,
            success: (modalRes) => {
              if (status === 'success' && modalRes.confirm) {
                // 更新本地状态
                this.setData({
                  'formData.check_time2': null,
                  'formData.check_user2': null,
                  'formData.fstate': '2',
                  'formData.in_time': null
                });
              }
            }
          });
        } else {
          wx.hideLoading();
          wx.showModal({
            title: '错误',
            content: '返回数据格式错误',
            showCancel: false
          });
        }
      },
      fail: (err) => {
        console.error('API Error:', err); // 调试：打印错误信息
        wx.hideLoading();
        wx.showModal({
          title: '错误',
          content: '提交失败，请重试',
          showCancel: false
        });
      },
      complete: () => {
        wx.hideLoading(); // 确保加载提示总是隐藏
      }
    });
  },

  // 结束访问按钮点击事件
  endVisit() {
    wx.showModal({
      title: '确认操作',
      content: '确定要结束访问吗？',
      success: (res) => {
        if (res.confirm) {
          // 设置结束状态并提交
          this.setData({
            'formData.is_over': true
          });
          this.submitForm();
        }
      }
    });
  }
});