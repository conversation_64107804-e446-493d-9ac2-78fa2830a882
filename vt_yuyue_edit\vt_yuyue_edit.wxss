Page {
  background: #fff;
  font-size: 26rpx;
}

.page_container {
  /* padding: 16px; */
  min-height: 100vh;
  padding-bottom: 120rpx; /* 调整底部padding，避免被底部菜单挡住 */
}

.page-header {
  margin-top: 20rpx;
  margin-bottom: 20rpx;
  display: flex;
}

.split {
  height: 20rpx;
  background-color: #f0f0f0;
}

.title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}

.status-tags-left {
  height: 50rpx;
  width: 4rpx;
  background-color: rgb(230, 0, 0);
  margin-right: 20rpx;
}

.status-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: space-between;
  flex: 1;
  line-height: 40rpx;
}

/* 姓名-ID标签左对齐 */
.name-id-tag {
  margin-left: 0 !important;
}

.fstate {
  width: 50rpx;
  height: 50rpx;
}

.tag {
  padding: 2px 15px;
  border-radius: 12px;
  font-size: 28rpx;
}

.tag-red {
  background-color: #e6f7ff;
  color: #f7093c;
}

.tag-green {
  background-color: #f6ffed;
  color: #52c41a;
}

.section {
  background-color: #fff;
  padding: 16rpx;
  margin-bottom: 16rpx;
}

.section-title {
  padding: 12px 16px;
  font-weight: bold;
  color: #333;
  border-bottom: 1px solid #f0f0f0;
}

.form-item {
  padding: 10rpx 20px;
  border-bottom: 1px solid #eaeaea;
  display: flex;

}

.form-item:last-child {
  border-bottom: none;
}

.form-label {
  width: 25%;
   line-height: 25px; 
   color: #747474;
}

.form-value {
  flex: 1;
  font-size: 26rpx;
  margin-top: 5rpx;
}

.disable {
  color: #b1b1b1;
}


.qrcode {
  width: 380rpx;
  height: 380rpx;
}



radio-group,
checkbox-group {
  display: flex;
  align-items: center;
}

checkbox-group label {
  margin-right: 20rpx;
}

/* 劳保物品checkbox样式 */
.ppe-checkbox-group {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
}

.ppe-checkbox-label {
  display: flex;
  align-items: center;
  margin-right: 20rpx;
  margin-bottom: 10rpx;
  white-space: nowrap;
  flex-shrink: 0;
}

radio,
checkbox {
  transform: scale(0.8);
}

radio+text,
checkbox+text {
  margin-left: 4px;
}

.image-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.image-item {
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: 4px;
  overflow: hidden;
}

.image-item image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.delete-btn {
  position: absolute;
  top: 0;
  right: 0;
  width: 20px;
  height: 20px;
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  font-size: 12px;
}

.upload-btn {
  width: 80px;
  height: 80px;
  border: 1px dashed #e5e5e5;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 12px;
}

.upload-btn .iconfont {
  margin-bottom: 4px;
}

.tips {
  font-size: 12px;
  color: #999;
  margin-top: 8px;
}

.btn-group {
  display: flex;
  gap: 16px;
  padding: 16px 0;
}

button {
  flex: 1;
  height: 40px;
  border-radius: 4px;
  font-size: 16px;
}

button[type="default"] {
  background-color: #fff;
  border: 1px solid #e5e5e5;
  color: #333;
}

button[type="primary"] {
  background-color: #165DFF;
  color: #fff;
  border: none;
}


.small07 {
  transform: scale(0.7);
  transform-origin: left center;
}

/* 底部操作栏样式 */
.bottomTabBar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background: #fff;
  border-top: 1rpx solid #bebdbd;
  display: flex;
  align-items: center;
  z-index: 999;
}

.fourTabBar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background: #fff;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  align-items: center;
  z-index: 999;
}

.oneTabBarTouch {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #666;
  padding: 10rpx 0;
}

.oneTabBarTouch image {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 8rpx;
}

.oneTabBarTouch:active {
  background: #f0f0f0;
}