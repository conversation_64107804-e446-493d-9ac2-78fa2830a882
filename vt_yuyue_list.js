let http = require('../../utils/https.js');
Page({
  /**
   * 页面的初始数据
   */
  data: {
    approvalListData: [],
    filterContent: '',
    totalArr: [],
    kind: 0, //0预约待审 1预约已审 2进入待审 3访问中 4访问结束
    page: 1,
    size: 20,
    clickFlag: false,
    ysTotal: 0,
    ysNum: 0,
    wsTotal: 0,
    wsNum: 0,
    lastTapTime: 0,
    ucode: '',
    visitorTypes: [], // Store visitor types
    selectedVisitorType: '', // Store selected visitor type
    quyuoptions: [], // Store region options
    selectedRegion: '', // Store selected region
    statusNames: ['预约待审', '预约已审', '进入待审', '访问中', '访问结束'] // 状态名称映射
  },

  onLoad() {
    let _this = this;
    wx.getStorage({
      key: 'ucode',
      success: function (res) {
        _this.setData({
          ucode: res.data
        });
        _this.login();
        _this.loadVisitorTypes(); // Load visitor types
        _this.loadOptions(); // Load region options
      },
      fail: function () {
        console.log('-----ucode-fail');
        wx.showToast({
          title: '获取用户标识失败',
          icon: 'none'
        });
      }
    });
  },

  login() {
    let _this = this;
    wx.getStorage({
      key: 'data',
      success: function (res) {
        console.log(res);
        let data = JSON.parse(res.data);
        _this.setData({
          personalData: data
        });
        _this.getData();
      },
      fail: function (err) {
        console.log(err);
        wx.showToast({
          title: '登录失败',
          icon: 'none'
        });
      }
    });
  },

  // Load visitor types
  loadVisitorTypes() {
    let _this = this;
    http.GET({
      url: 'all',
      params: {
        ucode: this.data.ucode,
        api_zpj: '/api/vistor/getvistorytype',
      },
      success: (res) => {
        console.log('getvistorytype', res);
        _this.setData({
          visitorTypes: res.data
        });
      },
      fail: function () {
        console.log('Failed to load visitor types');
        wx.showToast({
          title: '加载访客类型失败',
          icon: 'none'
        });
      }
    });
  },

  // Load region options
  loadOptions() {
    let _this = this;
    http.GET({
      url: 'all',
      params: {
        ucode: this.data.ucode,
        csCode: 'Vis_region',
        api_zpj: 'getOptionsByCode'
      },
      success: (res) => {
        console.log('getOptionsByCode', res);
        var vis_yuyue = res.data.split(',');
        _this.setData({
          quyuoptions: vis_yuyue
        });
      },
      fail: function () {
        console.log('Failed to load region options');
        wx.showToast({
          title: '加载区域选项失败',
          icon: 'none'
        });
      }
    });
  },

  chooseMsg(e) {
    var curTime = e.timeStamp;
    var lastTime = this.data.lastTapTime;
    let i = e.currentTarget.dataset.index;
    let arr = this.data.approvalListData;

    if (curTime - lastTime < 300) {
      let type = e.currentTarget.dataset.type;
      arr.map(function (e) {
        e.chooseFlag = false;
      });

      this.setData({
        approvalListData: arr
      });

      var chooseData = arr[i];
      var kind = this.data.kind;

      console.log('chooseData', chooseData);
      wx.navigateTo({
        url: `../vt_yuyue_edit/vt_yuyue_edit?id=${chooseData.id}&type=${type}&kind=${kind}`
      });
      console.log("挺快的双击，用了：" + curTime);
    } else {
      arr[i].chooseFlag = !arr[i].chooseFlag;
      this.setData({
        approvalListData: arr
      });
    }

    this.setData({
      lastTapTime: curTime
    });
  },

  chooseMsg2(e) {
    var id = e.currentTarget.dataset.id;
    var type = e.currentTarget.dataset.type;
    var tab = e.currentTarget.dataset.tab;
    var kind = this.data.kind;

    console.log('chooseMsg2', e);
    if (tab == '访客') {
      var url = `../vt_yuyue_edit/vt_yuyue_edit?id=${id}&type=${type}&kind=${kind}`;
      wx.navigateTo({
        url
      });
    }
  },



  changeKind(e) {
    let i = parseInt(e.currentTarget.dataset.index);
    console.log('切换状态到:', i, this.data.statusNames[i]);
    this.setData({
      kind: i,
      page: 1,
      ysTotal: 0,
      ysNum: 0,
      wsTotal: 0,
      wsNum: 0,
      approvalListData: [],
      filterContent: ""
    });
    this.getData();
  },

  filterChange(e) {
    let filter = e.detail.value;
    console.log(filter);

    this.setData({
      filterContent: filter,
      page: 1,
      approvalListData: []
    });
    this.getData();
  },

  doReload() {
    this.setData({
      page: 1,
      ysTotal: 0,
      ysNum: 0,
      wsTotal: 0,
      wsNum: 0,
      approvalListData: [],
      filterContent: ""
    });
    this.getData();
  },
  parseDateStr(str) {
    let [datePart, timePart] = str.split(' ');
    let [y, m, d] = datePart.split('-');
    let [hh, mm, ss] = timePart.split(':');
    return new Date(y, m - 1, d, hh, mm, ss);
  },
  getData() {
    var _this = this;
    let filter = this.data.filterContent;
    let kind = this.data.kind;
    let page = this.data.page;
    console.log(page)
    let size = this.data.size;

    let ysTotal = this.data.ysTotal;
    let ysNum = this.data.ysNum;
    let wsTotal = this.data.wsTotal;
    let wsNum = this.data.wsNum;
    console.log('getData', page,ysNum,ysTotal,(page-1)*20);

    if ((page-1)*20 <= ysNum || ysTotal == 0) {
      http.GET({
        url: 'getapprovallist',
        params: {
          empno: _this.data.personalData.empno,
          empname: _this.data.personalData.empname,
          kind: kind,
          filter: filter,
          page: page,
          size: size,
          ucode: _this.data.ucode
        },
        success: function (res) {
          console.log('getapprovallist', res);
          let arr = _this.data.approvalListData;
          let data = res.data.datas;

          ysNum += data.length;
          ysTotal = res.data.total;
          data.map((e, i) => {
            // 根据kind设置flag状态
            e.flag = kind;
            e.chooseFlag = false;
            e.bgcolor = e.bgcolor == '' ? '#ccc' : '#' + e.bgcolor;

            if (e.tabType == '访客') {
              let d = _this.parseDateStr(e.leavedate);
              e.leavedate = _this.formatDate(d, 'MM/DD HH:mm');
            }

            arr.push(e);
          });
          _this.setData({
            approvalListData: arr,
            totalArr: arr,
            ysNum: ysNum,
            ysTotal: ysTotal
          });
        },
        fail: function () {
          console.log('Failed to load approval list');
          wx.showToast({
            title: '加载审批列表失败',
            icon: 'none'
          });
        }
      });
    }

    console.log(998,page)
    page++;
    console.log(999,page)
    _this.setData({
      page: page
    });
  },

  formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
    const pad = (num) => num.toString().padStart(2, '0');

    const map = {
      YYYY: date.getFullYear(),
      MM: pad(date.getMonth() + 1),
      DD: pad(date.getDate()),
      HH: pad(date.getHours()),
      mm: pad(date.getMinutes()),
      ss: pad(date.getSeconds()),
    };

    return format.replace(/YYYY|MM|DD|HH|mm|ss/g, (matched) => map[matched]);
  },

  approvalClick() {
    var _this = this;
    let data = this.data.approvalListData;
    let str = '';
    let flag = false;
    data.map((e) => {
      str += e.chooseFlag ? `${e.tablename}|${e.kind}|${e.id},` : '';
      if (e.flag) {
        flag = true;
      }
    });
    if (flag) {
      wx.showModal({
        title: '提示',
        content: '已经审批，不能再次审批',
        success: function (res) {},
        fail: function (res) {},
      });
      return;
    }
    str = str.slice(0, str.length - 1);
    console.log(str);
    http.GET({
      url: 'approval',
      params: {
        empno: _this.data.personalData.empno,
        data: str,
        ucode: _this.data.ucode
      },
      success: function (res) {
        wx.showModal({
          title: '提示',
          content: res.data.msg,
          showCancel: false,
          success: function (res) {
            if (res.confirm) {
              _this.setData({
                filterContent: "",
                page: 1,
                ysTotal: 0,
                ysNum: 0,
                wsTotal: 0,
                wsNum: 0,
                approvalListData: []
              });
              _this.getData();
            }
          }
        });
      },
      fail: function () {
        console.log('Approval failed');
        wx.showToast({
          title: '审批失败',
          icon: 'none'
        });
      }
    });
  },

  goUpload(e) {
    let type = e.currentTarget.dataset.type;
    let kind = this.data.kind;
    let approvalListData = this.data.approvalListData;
    let chooseData = [];
    let _this = this;

    approvalListData.forEach(e => {
      if (e.chooseFlag && !(e.tabType == '访客' && e.empname == "提货送货")) {
        chooseData.push(e);
      }
    });

    console.log('操作类型:', type, '当前状态:', kind, '选中数据:', chooseData);
    if (chooseData.length <= 0) {
      wx.showModal({
        title: '提示',
        content: '请先选中',
        showCancel: false,
        success: function (res) {}
      });
      return;
    }

    // 根据不同的操作类型和状态执行不同的逻辑
    if (type == "0") { // 审批操作
      this.handleApproval(chooseData, kind);
    } else if (type == "1") { // 结束访问操作
      this.handleEndVisit(chooseData);
    }
  },

  // 处理审批操作
  handleApproval(chooseData, kind) {
    let _this = this;
    // Show modal to prompt for visitor type and region selection
    wx.showModal({
      title: '选择审批信息',
      content: '请选择访客类型和所属区域以继续审批',
      showCancel: true,
      cancelText: '取消',
      confirmText: '确定',
      success: (res) => {
        if (res.confirm) {
          // Show picker for visitor types
          wx.showActionSheet({
            itemList: _this.data.visitorTypes.map(type => type.label),
            success: (pickerRes) => {
              const selectedType = _this.data.visitorTypes[pickerRes.tapIndex].value;
              _this.setData({
                selectedVisitorType: selectedType
              });

              // Show picker for regions
              wx.showActionSheet({
                itemList: _this.data.quyuoptions,
                success: (regionRes) => {
                  const selectedRegion = _this.data.quyuoptions[regionRes.tapIndex];
                  _this.setData({
                    selectedRegion: selectedRegion
                  });

                  // Proceed with approval
                  http.GET({
                    url: 'canapproval',
                    params: {
                      tabType: chooseData[0].tabType,
                      allselectid: chooseData[0].allselectid,
                      kind: kind,
                      Checker: _this.data.personalData.empname,
                      ucode: _this.data.ucode,
                      visitorType: selectedType,
                      region: selectedRegion
                    },
                    success: function (res) {
                      console.log(res);
                      wx.showModal({
                        title: '提示',
                        content: res.data.msg,
                        showCancel: false,
                        success: function (res) {
                          if (res.confirm) {
                            _this.refreshData();
                          }
                        }
                      });
                    },
                    fail: function () {
                      wx.showToast({
                        title: '审批失败',
                        icon: 'none'
                      });
                    }
                  });
                },
                fail: (regionRes) => {
                  console.log('Region picker cancelled');
                  wx.showToast({
                    title: '未选择区域',
                    icon: 'none'
                  });
                }
              });
            },
            fail: (pickerRes) => {
              console.log('Visitor type picker cancelled');
              wx.showToast({
                title: '未选择访客类型',
                icon: 'none'
              });
            }
          });
        }
      },
      fail: (res) => {
        console.log('Modal cancelled');
      }
    });
  },

  // 处理结束访问操作
  handleEndVisit(chooseData) {
    let _this = this;
    wx.showModal({
      title: '确认操作',
      content: '确定要结束选中的访问吗？',
      success: (res) => {
        if (res.confirm) {
          // 调用结束访问的API
          http.GET({
            url: 'endvisit',
            params: {
              ids: chooseData.map(item => item.id).join(','),
              ucode: _this.data.ucode
            },
            success: function (res) {
              wx.showModal({
                title: '提示',
                content: res.data.msg || '操作成功',
                showCancel: false,
                success: function (res) {
                  if (res.confirm) {
                    _this.refreshData();
                  }
                }
              });
            },
            fail: function () {
              wx.showToast({
                title: '操作失败',
                icon: 'none'
              });
            }
          });
        }
      }
    });
  },

  // 刷新数据的公共方法
  refreshData() {
    this.setData({
      filterContent: "",
      page: 1,
      ysTotal: 0,
      ysNum: 0,
      wsTotal: 0,
      wsNum: 0,
      approvalListData: []
    });
    this.getData();
  },

  goPages(e) {
    let thisPage = this.data.pages;
    let toPage = e.currentTarget.dataset.pages;
    if (thisPage != toPage) {
      wx.reLaunch({
        url: `../${toPage}/${toPage}`
      });
    }
  },

  viewQrCode(e) {
    let id = e.currentTarget.dataset.id;
    let imageUrl = http.ROOT_URL + '/api/vistor/qrcode?applyid=' + id;
    console.log(imageUrl);
    wx.previewImage({
      urls: [imageUrl],
      current: imageUrl
    });
  },

  onShow: function (options) {},

  onReachBottom() {
    this.getData();
  },

  onShareAppMessage: function (res) {
    if (res.from === 'button') {
      console.log("来自页面内转发按钮");
      console.log(res.target);
    } else {
      console.log("来自右上角转发菜单");
    }
    return {
      title: '宏道科技',
      path: '/pages/index/index',
      imageUrl: "/images/12.png",
      success: (res) => {
        console.log("转发成功", res);
      },
      fail: (res) => {
        console.log("转发失败", res);
      }
    };
  }
});