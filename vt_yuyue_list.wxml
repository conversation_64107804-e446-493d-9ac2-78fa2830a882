<!--pages/vt_yuyue/list.wxml-->
<!--状态标签按钮-->
<view class="split"></view>
<view class='statusTabsContainer'>
    <view class='statusTabs'>
        <view class="statusTab {{kind==0?'active':''}}" data-index="0" bindtap='changeKind'>预约待审</view>
        <view class="statusTab {{kind==1?'active':''}}" data-index="1" bindtap='changeKind'>预约已审</view>
        <view class="statusTab {{kind==2?'active':''}}" data-index="2" bindtap='changeKind'>进入待审</view>
        <view class="statusTab {{kind==3?'active':''}}" data-index="3" bindtap='changeKind'>访问中</view>
        <!-- <view class="statusTab {{kind==4?'active':''}}" data-index="4" bindtap='changeKind'>访问结束</view> -->
    </view>
</view>
<!--状态标签按钮-->

<view class='approvalListBox'>
    <block wx:for="{{approvalListData}}" wx:key="itemKey" wx:for-item="item" wx:for-index="itemIndex">
        <view class='approvalList_li  {{item.chooseFlag?"chooseColor":""}}'>
            <view class='will' style="background:{{item.bgcolor}}"></view>
            <view class="no-wrap" data-index='{{itemIndex}}' data-tab='{{item.tabType}}' data-type='{{item.flag}}' data-id='{{item.id}}' bindtap='chooseMsg2'>
                <image src='../../icon/yjrspl.png' wx:if="{{item.flag==0 || item.flag==2}}"></image>
                <!-- <image src='../../icon/wjrspl.png' wx:if="{{item.flag==2}}"></image> -->
                <image src='../../icon/yjsp.png' wx:if="{{item.flag==1 || item.flag==3}}"></image>
                <!-- {{item.CheckFlag}} -->
            </view>
            <view class="no-wrap" data-index='{{itemIndex}}' data-type='{{item.flag}}' data-id='{{item.id}}' bindtap='chooseMsg'>
                <text class='approvalList_li_CNname'>{{item.tabType}}</text>
                <text class='approvalList_li_ENname'>{{item.empname}}</text>
                <text class='approvalList_li_ENname'>{{item.empno}}</text>
                <text class='approvalList_li_ENname'>{{item.leavedate}}</text>

                <text class='approvalList_li_ENname' wx:if="{{item.tabType!='访客'}}">{{item.leavetime}}</text>
                <text class='approvalList_li_ENname' wx:if="{{item.tabType!='访客'}}">{{item.Checker}}</text>
            </view>
            <!-- <text class='approvalList_li_ENname' bindtap='viewQrCode' data-id="{{item.id}}" style="color:blue;" wx:if="{{item.tabType=='访客'&&item.Checker}}">查看
                通行证
            </text> -->

        </view>
    </block>
</view>
<!--底部栏-->
<view class='twoTabBar comTabBar' wx:if="{{kind==0||kind==2}}">
    <view class='oneTabBarTouch' bindtap='goPages' data-pages="home">
        <image src='../../icon/home.png'></image>
        返回主页
    </view>
    <view class='oneTabBarTouch' bindtap='goUpload' data-type="0">
        <image src='../../icon/sp.png'></image>
        审批
    </view>
</view>

<view class='twoTabBar comTabBar' wx:if="{{kind==1||kind==3||kind==4}}">
    <view class='oneTabBarTouch' bindtap='goPages' data-pages="home">
        <image src='../../icon/home.png'></image>
        返回主页
    </view>
    <view class='oneTabBarTouch' bindtap='goUpload' data-type="0" wx:if="{{kind==1}}">
        <image src='../../icon/delete.png'></image>
        退审
    </view>
    <view class='oneTabBarTouch' bindtap='goUpload' data-type="1" wx:if="{{kind==3}}">
        <image src='../../icon/sp.png'></image>
        结束访问
    </view>
</view>