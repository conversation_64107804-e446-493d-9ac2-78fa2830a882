/* pages/approval/approval.wxss */
Page {
  background-color: #fff;
  font-size: 26rpx;
}

.split {
height: 20rpx;
background-color: #f0f0f0;
}

/* 状态标签容器 */
.statusTabsContainer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  z-index: 999;
}

.statusTabs {
  display: flex;
  padding: 0 20rpx;
}

/* 状态标签按钮 */
.statusTab {
  flex: 1;
  padding: 6rpx 10rpx;
  margin-right: 15rpx;
  background: #f8f8f8;
  color: #666;
  font-size: 26rpx;
  border-radius: 50rpx;
  border: 1rpx solid #e0e0e0;
  transition: all 0.3s ease;
  text-align: center;
  white-space: nowrap;
}

.statusTab:last-child {
  margin-right: 0;
}

.statusTab.active {
  background: linear-gradient(135deg, #f1e44b 0%, #f7f7f7 100%);
  /* background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);*/
  /*color: #fff;*/
  /*border-color: #667eea;*/
  box-shadow: 0 4rpx 12rpx #e4e4e4;
  transform: translateY(-2rpx);
}

.statusTab:not(.active):hover {
  background: #f0f0f0;
  border-color: #ccc;
}

.approvalListBox {
  margin-top: 120rpx; /* 为固定的状态标签预留空间 */
  background: #fff;
  padding-bottom: 120rpx; /* 为底部操作栏预留空间 */
}

.approvalList_li_time {
  font-size: 20rpx;
  padding-right: 9rpx;
}

.approvalList_li_time.opacity0 {
  opacity: 0
}

.approvalListBox .approvalList_li {
  width: 100%;
  height: 80rpx;
  border-bottom: 1rpx solid #f0f0f0;
  padding-left: 108rpx;
  padding-top: 10rpx;
  padding-bottom: 10rpx;
  box-sizing: border-box;
  position: relative;
  display: flex;
  justify-content: space-between;
  font-size: 26rpx;
  align-items: center;
}

.no-wrap {
  display: flex;
  flex-wrap: nowrap;
  /* 禁止换行 */
  white-space: nowrap;
  /* 禁止文本换行 */
  overflow-x: auto;
  /* 添加横向滚动条（可选） */
}

.approvalListBox .approvalList_li.chooseColor {
  background: #f0f0f0;
}

.approvalListBox .approvalList_li .will {
  width: 8rpx;
  height: 66rpx;
  content: '';

  position: absolute;
  left: 8rpx;
  top: 50%;
  z-index: 1;
  transform: translateY(-50%);
}

.approvalListBox .approvalList_li image {
  position: absolute;
  left: 38rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 50rpx;
  height: 50rpx;
}

.approvalListBox .approvalList_li text {
  /* line-height: 118rpx; */

  display: inline-block;
}

.approvalListBox .approvalList_li .approvalList_li_CNname {
  width: 90rpx;
  padding-left: 10rpx;
}

.approvalListBox .approvalList_li .approvalList_li_ENname {
  width: 160rpx;
  /* overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis */
}
/* 底部操作栏样式 */
.twoTabBar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background: #fff;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  align-items: center;
  z-index: 999;
}

.oneTabBarTouch {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #666;
  padding: 10rpx 0;
}

.oneTabBarTouch image {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 8rpx;
}

.oneTabBarTouch:active {
  background: #f0f0f0;
}

